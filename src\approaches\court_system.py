"""
Court System approach for loan approval prediction.
"""

import time
import re
from typing import Dict, Any, Optional
from datetime import datetime

from .base import Loan<PERSON><PERSON><PERSON>alA<PERSON>roach, PromptTemplate
from ..models.loan_data import LoanData
from ..models.llm_response import CourtSystemResponse, LoanDecision, ConfidenceLevel, RiskLevel
from ..providers.base import LLMProvider


class CourtSystemApproach(LoanApprovalApproach):
    """
    Court System approach using three LLMs: pros advocate, cons analyst, and final judge.
    
    This approach simulates a legal court system where:
    1. A pros advocate LL<PERSON> presents arguments FOR loan approval
    2. A cons analyst LLM presents arguments AGAINST loan approval  
    3. A judge <PERSON><PERSON> makes the final decision based on both arguments
    """
    
    def __init__(self, 
                 pros_provider: LLMProvider, 
                 cons_provider: LLMProvider, 
                 judge_provider: LLMProvider,
                 name: str = "Court System"):
        super().__init__(name)
        self.pros_provider = pros_provider
        self.cons_provider = cons_provider
        self.judge_provider = judge_provider
        self.prompt_template = PromptTemplate()
    
    def predict_single(self, loan_data: LoanData) -> CourtSystemResponse:
        """
        Predict loan approval using the court system approach.
        
        Args:
            loan_data: Loan application data
            
        Returns:
            CourtSystemResponse with final decision and all analyses
        """
        start_time = time.time()
        
        try:
            # Step 1: Get pros analysis
            pros_analysis = self._get_pros_analysis(loan_data)
            
            # Step 2: Get cons analysis
            cons_analysis = self._get_cons_analysis(loan_data)
            
            # Step 3: Get final judge decision
            judge_decision = self._get_judge_decision(loan_data, pros_analysis, cons_analysis)
            
            # Parse judge decision
            parsed_decision = self._parse_judge_response(judge_decision)
            
            # Calculate deliberation metrics
            deliberation_metrics = self._calculate_deliberation_metrics(pros_analysis, cons_analysis)
            
            processing_time = time.time() - start_time
            
            # Create court system response
            response = CourtSystemResponse(
                decision=parsed_decision["decision"],
                confidence=parsed_decision["confidence"],
                risk_assessment=parsed_decision["risk_assessment"],
                reasoning=parsed_decision["reasoning"],
                key_factors=parsed_decision["key_factors"],
                positive_factors=parsed_decision["positive_factors"],
                negative_factors=parsed_decision["negative_factors"],
                approval_probability=parsed_decision.get("approval_probability"),
                default_probability=parsed_decision.get("default_probability"),
                model_name=f"Court System ({self.judge_provider.model_name})",
                processing_time=processing_time,
                pros_analysis=pros_analysis,
                cons_analysis=cons_analysis,
                judge_reasoning=parsed_decision["reasoning"],
                pros_model=self.pros_provider.model_name,
                cons_model=self.cons_provider.model_name,
                judge_model=self.judge_provider.model_name,
                pros_strength=deliberation_metrics["pros_strength"],
                cons_strength=deliberation_metrics["cons_strength"]
            )
            
            return response
            
        except Exception as e:
            processing_time = time.time() - start_time
            
            # Return error response
            return CourtSystemResponse(
                decision=LoanDecision.DENY,
                confidence=ConfidenceLevel.VERY_LOW,
                risk_assessment=RiskLevel.VERY_HIGH,
                reasoning=f"Error in court system evaluation: {str(e)}",
                key_factors=["System Error"],
                negative_factors=[f"Court system processing error: {str(e)}"],
                model_name="Court System (Error)",
                processing_time=processing_time,
                pros_analysis="Error in pros analysis",
                cons_analysis="Error in cons analysis",
                judge_reasoning=f"Error in judge decision: {str(e)}",
                pros_model=self.pros_provider.model_name,
                cons_model=self.cons_provider.model_name,
                judge_model=self.judge_provider.model_name
            )
    
    def _get_pros_analysis(self, loan_data: LoanData) -> str:
        """Get pros analysis from the advocate LLM."""
        prompt = self.prompt_template.format_pros_analysis(loan_data)
        
        try:
            response = self.pros_provider.generate(prompt)
            if response.get("success", False):
                return response["response"]
            else:
                return f"Error in pros analysis: {response.get('error', 'Unknown error')}"
        except Exception as e:
            return f"Error generating pros analysis: {str(e)}"
    
    def _get_cons_analysis(self, loan_data: LoanData) -> str:
        """Get cons analysis from the risk analyst LLM."""
        prompt = self.prompt_template.format_cons_analysis(loan_data)
        
        try:
            response = self.cons_provider.generate(prompt)
            if response.get("success", False):
                return response["response"]
            else:
                return f"Error in cons analysis: {response.get('error', 'Unknown error')}"
        except Exception as e:
            return f"Error generating cons analysis: {str(e)}"
    
    def _get_judge_decision(self, loan_data: LoanData, pros_analysis: str, cons_analysis: str) -> str:
        """Get final decision from the judge LLM."""
        prompt = self.prompt_template.format_judge_decision(loan_data, pros_analysis, cons_analysis)
        
        try:
            response = self.judge_provider.generate(prompt)
            if response.get("success", False):
                return response["response"]
            else:
                return f"Error in judge decision: {response.get('error', 'Unknown error')}"
        except Exception as e:
            return f"Error generating judge decision: {str(e)}"
    
    def _parse_judge_response(self, response_text: str) -> Dict[str, Any]:
        """Parse the judge's response into structured data."""
        parsed = {
            "decision": LoanDecision.DENY,  # Default to deny for safety
            "confidence": ConfidenceLevel.LOW,
            "risk_assessment": RiskLevel.HIGH,
            "reasoning": "",
            "key_factors": [],
            "positive_factors": [],
            "negative_factors": [],
            "approval_probability": None,
            "default_probability": None
        }
        
        try:
            # Extract final decision
            decision_match = re.search(r'(?:FINAL_DECISION|Final Decision):\s*\[?([A-Z_]+)\]?', response_text, re.IGNORECASE)
            if decision_match:
                decision_str = decision_match.group(1).upper()
                if decision_str in ["APPROVE", "APPROVED"]:
                    parsed["decision"] = LoanDecision.APPROVE
                elif decision_str in ["DENY", "DENIED", "REJECT", "REJECTED"]:
                    parsed["decision"] = LoanDecision.DENY
                elif decision_str in ["CONDITIONAL"]:
                    parsed["decision"] = LoanDecision.CONDITIONAL
            
            # Extract confidence
            confidence_match = re.search(r'(?:CONFIDENCE|Confidence):\s*\[?([A-Z_]+)\]?', response_text, re.IGNORECASE)
            if confidence_match:
                confidence_str = confidence_match.group(1).upper().replace(" ", "_")
                try:
                    parsed["confidence"] = ConfidenceLevel(confidence_str)
                except ValueError:
                    pass
            
            # Extract risk assessment
            risk_match = re.search(r'(?:RISK_ASSESSMENT|Risk Assessment):\s*\[?([A-Z_]+)\]?', response_text, re.IGNORECASE)
            if risk_match:
                risk_str = risk_match.group(1).upper().replace(" ", "_")
                try:
                    parsed["risk_assessment"] = RiskLevel(risk_str)
                except ValueError:
                    pass
            
            # Extract judicial reasoning
            reasoning_match = re.search(r'(?:JUDICIAL_REASONING|Judicial Reasoning|DECISION_RATIONALE|Decision Rationale):\s*(.*?)(?=\n\d+\.|$)', response_text, re.IGNORECASE | re.DOTALL)
            if reasoning_match:
                parsed["reasoning"] = reasoning_match.group(1).strip()
            else:
                # Fallback: use the entire response as reasoning
                parsed["reasoning"] = response_text.strip()
            
            # Extract key factors
            key_factors_match = re.search(r'(?:KEY_FACTORS|Key Factors):\s*(.*?)(?=\n\d+\.|$)', response_text, re.IGNORECASE | re.DOTALL)
            if key_factors_match:
                factors_text = key_factors_match.group(1)
                parsed["key_factors"] = self._extract_list_items(factors_text)
            
            # Extract positive and negative factors from the reasoning
            # Since this is the judge's final decision, we extract factors from the full context
            if "positive" in response_text.lower() or "strength" in response_text.lower():
                positive_factors = self._extract_factors_from_text(response_text, positive=True)
                parsed["positive_factors"] = positive_factors
            
            if "negative" in response_text.lower() or "risk" in response_text.lower() or "concern" in response_text.lower():
                negative_factors = self._extract_factors_from_text(response_text, positive=False)
                parsed["negative_factors"] = negative_factors
            
        except Exception as e:
            parsed["reasoning"] = f"Failed to parse judge response: {str(e)}\n\nRaw response:\n{response_text}"
        
        return parsed
    
    def _extract_list_items(self, text: str) -> list:
        """Extract list items from text."""
        items = []
        lines = text.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Remove common list markers
            line = re.sub(r'^[-*•]\s*', '', line)
            line = re.sub(r'^\d+\.\s*', '', line)
            line = re.sub(r'^[a-zA-Z]\.\s*', '', line)
            
            if line:
                items.append(line)
        
        if not items and ',' in text:
            items = [item.strip() for item in text.split(',') if item.strip()]
        
        return items[:10]
    
    def _extract_factors_from_text(self, text: str, positive: bool = True) -> list:
        """Extract positive or negative factors from text."""
        factors = []
        
        if positive:
            # Look for positive indicators
            positive_patterns = [
                r'high credit score',
                r'strong income',
                r'stable employment',
                r'low debt-to-income',
                r'good payment history',
                r'adequate collateral',
                r'positive cash flow'
            ]
            
            for pattern in positive_patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    factors.append(pattern.replace(r'', '').title())
        else:
            # Look for negative indicators
            negative_patterns = [
                r'high debt-to-income',
                r'low credit score',
                r'unstable employment',
                r'insufficient income',
                r'payment delinquencies',
                r'high risk',
                r'poor credit history'
            ]
            
            for pattern in negative_patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    factors.append(pattern.replace(r'', '').title())
        
        return factors[:5]
    
    def _calculate_deliberation_metrics(self, pros_analysis: str, cons_analysis: str) -> Dict[str, float]:
        """Calculate metrics for the strength of pros vs cons arguments."""
        # Simple heuristic based on text length and keyword density
        pros_length = len(pros_analysis.split())
        cons_length = len(cons_analysis.split())
        
        # Count positive/negative keywords
        positive_keywords = ['approve', 'strong', 'good', 'excellent', 'stable', 'adequate', 'sufficient']
        negative_keywords = ['deny', 'risk', 'concern', 'poor', 'insufficient', 'unstable', 'high risk']
        
        pros_positive_count = sum(1 for word in positive_keywords if word in pros_analysis.lower())
        cons_negative_count = sum(1 for word in negative_keywords if word in cons_analysis.lower())
        
        # Calculate relative strengths (0.0 to 1.0)
        total_length = pros_length + cons_length
        if total_length > 0:
            pros_strength = (pros_length + pros_positive_count * 10) / (total_length + 20)
            cons_strength = (cons_length + cons_negative_count * 10) / (total_length + 20)
        else:
            pros_strength = 0.5
            cons_strength = 0.5
        
        # Normalize to ensure they don't exceed 1.0
        pros_strength = min(pros_strength, 1.0)
        cons_strength = min(cons_strength, 1.0)
        
        return {
            "pros_strength": pros_strength,
            "cons_strength": cons_strength
        }
    
    def get_approach_info(self) -> Dict[str, Any]:
        """Get information about this approach."""
        info = super().get_approach_info()
        info.update({
            "pros_model": {
                "provider": self.pros_provider.provider_name,
                "model_name": self.pros_provider.model_name
            },
            "cons_model": {
                "provider": self.cons_provider.provider_name,
                "model_name": self.cons_provider.model_name
            },
            "judge_model": {
                "provider": self.judge_provider.provider_name,
                "model_name": self.judge_provider.model_name
            }
        })
        return info

"""
Data loading utilities for loan data processing.
"""

import pandas as pd
import polars as pl
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import numpy as np
from tqdm import tqdm

from ..models.loan_data import LoanData, LoanBatch, LoanStatus


class LoanDataLoader:
    """Loader for loan data from CSV files."""

    def __init__(self, use_polars: bool = True):
        """
        Initialize data loader.

        Args:
            use_polars: Whether to use Polars for large file processing
        """
        self.use_polars = use_polars
        self.required_columns = [
            'loan_amnt', 'int_rate', 'grade', 'annual_inc', 'dti', 'loan_status'
        ]
        self.optional_columns = [
            'fico_range_low', 'fico_range_high'
        ]

    def load_csv(self,
                 file_path: str,
                 chunk_size: Optional[int] = None,
                 max_records: Optional[int] = None,
                 sample_rate: Optional[float] = None) -> List[LoanData]:
        """
        Load loan data from CSV file.

        Args:
            file_path: Path to CSV file
            chunk_size: Size of chunks for processing large files
            max_records: Maximum number of records to load
            sample_rate: Random sampling rate (0.0 to 1.0)

        Returns:
            List of LoanData objects
        """
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        print(f"Loading loan data from {file_path}")

        if self.use_polars:
            return self._load_with_polars(file_path, max_records, sample_rate)
        else:
            return self._load_with_pandas(file_path, chunk_size, max_records, sample_rate)

    def _load_with_polars(self,
                          file_path: Path,
                          max_records: Optional[int] = None,
                          sample_rate: Optional[float] = None) -> List[LoanData]:
        """Load data using Polars for better performance with large files."""
        try:
            # If max_records is small, use streaming to avoid loading entire file
            if max_records and max_records <= 1000:
                print(
                    f"Using streaming read for small max_records ({max_records})")
                return self._load_with_polars_streaming(file_path, max_records, sample_rate)

            # For larger datasets or no limit, try regular read with memory management
            try:
                # Read CSV with Polars
                df = pl.read_csv(str(file_path))

                print(f"Loaded {len(df)} records from CSV")

                # Apply sampling if specified
                if sample_rate and 0 < sample_rate < 1:
                    df = df.sample(fraction=sample_rate, seed=42)
                    print(
                        f"Sampled to {len(df)} records ({sample_rate*100:.1f}%)")

                # Apply max records limit
                if max_records and len(df) > max_records:
                    df = df.head(max_records)
                    print(f"Limited to {max_records} records")

                # Convert to pandas for processing (Polars -> Pandas conversion)
                df_pandas = df.to_pandas()

                return self._process_dataframe(df_pandas)

            except MemoryError:
                print("Memory error with full load, trying streaming approach...")
                return self._load_with_polars_streaming(file_path, max_records, sample_rate)

        except Exception as e:
            print(f"Error loading with Polars: {e}")
            print("Falling back to Pandas...")
            return self._load_with_pandas(file_path, None, max_records, sample_rate)

    def _load_with_polars_streaming(self,
                                    file_path: Path,
                                    max_records: Optional[int] = None,
                                    sample_rate: Optional[float] = None) -> List[LoanData]:
        """Load data using Polars streaming for memory efficiency."""
        try:
            # Use scan_csv for lazy loading, then collect only what we need
            lazy_df = pl.scan_csv(str(file_path))

            # Apply max records limit early
            if max_records:
                lazy_df = lazy_df.head(max_records)
                print(f"Streaming read limited to {max_records} records")

            # Collect the limited data
            df = lazy_df.collect()

            print(f"Loaded {len(df)} records via streaming")

            # Apply sampling if specified (after limiting records)
            if sample_rate and 0 < sample_rate < 1:
                df = df.sample(fraction=sample_rate, seed=42)
                print(f"Sampled to {len(df)} records ({sample_rate*100:.1f}%)")

            # Convert to pandas for processing
            df_pandas = df.to_pandas()

            return self._process_dataframe(df_pandas)

        except Exception as e:
            print(f"Error with Polars streaming: {e}")
            # Fall back to pandas chunking
            return self._load_with_pandas(file_path, 1000, max_records, sample_rate)

    def _load_with_pandas(self,
                          file_path: Path,
                          chunk_size: Optional[int] = None,
                          max_records: Optional[int] = None,
                          sample_rate: Optional[float] = None) -> List[LoanData]:
        """Load data using Pandas with chunking support."""
        loan_data_list = []
        total_processed = 0

        # For small max_records, use nrows parameter to avoid loading entire file
        if max_records and max_records <= 1000:
            try:
                print(
                    f"Using nrows parameter for small dataset ({max_records} records)")
                # Read only the number of rows we need (plus some buffer for sampling)
                nrows_to_read = max_records * 10 if sample_rate else max_records
                df = pd.read_csv(
                    file_path, nrows=nrows_to_read, low_memory=False)

                print(f"Read {len(df)} rows from CSV")

                # Apply sampling
                if sample_rate and 0 < sample_rate < 1:
                    df = df.sample(frac=sample_rate, random_state=42)
                    print(f"Sampled to {len(df)} records")

                # Apply max records limit
                if max_records and len(df) > max_records:
                    df = df.head(max_records)
                    print(f"Limited to {max_records} records")

                return self._process_dataframe(df)

            except Exception as e:
                print(f"Error with nrows approach: {e}")
                # Fall back to chunking
                chunk_size = 1000

        # Determine if we need chunking for larger datasets
        if chunk_size is None:
            # Try to load entire file
            try:
                df = pd.read_csv(file_path, low_memory=False)

                # Apply sampling
                if sample_rate and 0 < sample_rate < 1:
                    df = df.sample(frac=sample_rate, random_state=42)

                # Apply max records limit
                if max_records and len(df) > max_records:
                    df = df.head(max_records)

                return self._process_dataframe(df)

            except MemoryError:
                print("File too large for memory, using chunking...")
                chunk_size = 10000

        # Process in chunks
        print(f"Processing file in chunks of {chunk_size}")
        chunk_iter = pd.read_csv(
            file_path, chunksize=chunk_size, low_memory=False)

        for chunk in tqdm(chunk_iter, desc="Processing chunks"):
            # Apply sampling to chunk
            if sample_rate and 0 < sample_rate < 1:
                chunk = chunk.sample(frac=sample_rate, random_state=42)

            # Process chunk
            chunk_loan_data = self._process_dataframe(chunk)
            loan_data_list.extend(chunk_loan_data)

            total_processed += len(chunk_loan_data)

            # Check max records limit
            if max_records and total_processed >= max_records:
                loan_data_list = loan_data_list[:max_records]
                break

        print(f"Processed {len(loan_data_list)} loan records")
        return loan_data_list

    def _process_dataframe(self, df: pd.DataFrame) -> List[LoanData]:
        """Process pandas DataFrame into LoanData objects."""
        # Check for required columns
        missing_columns = [
            col for col in self.required_columns if col not in df.columns]
        if missing_columns:
            print(f"Warning: Missing required columns: {missing_columns}")

        # Clean and preprocess data
        df = self._clean_dataframe(df)

        # Convert to LoanData objects
        loan_data_list = []

        for _, row in tqdm(df.iterrows(), total=len(df), desc="Converting to LoanData"):
            try:
                loan_data = self._row_to_loan_data(row)
                if loan_data:
                    loan_data_list.append(loan_data)
            except Exception as e:
                # Skip problematic rows
                continue

        return loan_data_list

    def _clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and preprocess the DataFrame."""
        # Make a copy to avoid modifying original
        df = df.copy()

        # Handle percentage columns (remove % and convert to float)
        percentage_columns = ['int_rate', 'revol_util']
        for col in percentage_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).str.replace(
                    '%', '').replace('', np.nan)
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Clean employment length
        if 'emp_length' in df.columns:
            df['emp_length'] = df['emp_length'].fillna('n/a')

        # Handle missing values for key numeric columns
        numeric_columns = ['annual_inc', 'dti',
                           'delinq_2yrs', 'inq_last_6mths', 'pub_rec']
        for col in numeric_columns:
            if col in df.columns:
                if col in ['delinq_2yrs', 'inq_last_6mths', 'pub_rec']:
                    df[col] = df[col].fillna(0)  # Count columns default to 0
                else:
                    # Use median for financial metrics
                    df[col] = df[col].fillna(df[col].median())

        # Clean categorical columns
        categorical_columns = ['grade', 'home_ownership',
                               'verification_status', 'purpose']
        for col in categorical_columns:
            if col in df.columns:
                df[col] = df[col].fillna('Unknown')

        return df

    def _row_to_loan_data(self, row: pd.Series) -> Optional[LoanData]:
        """Convert a pandas Series row to LoanData object."""
        try:
            # Map row data to LoanData fields
            loan_data_dict = {}

            # Required fields
            required_mappings = {
                'loan_amnt': 'loan_amnt',
                'int_rate': 'int_rate',
                'grade': 'grade',
                'annual_inc': 'annual_inc',
                'dti': 'dti'
            }

            for loan_field, csv_field in required_mappings.items():
                if csv_field in row.index and pd.notna(row[csv_field]):
                    loan_data_dict[loan_field] = row[csv_field]
                else:
                    # Skip this row if required field is missing
                    return None

            # Optional fields
            optional_mappings = {
                'installment': 'installment',
                'sub_grade': 'sub_grade',
                'emp_length': 'emp_length',
                'emp_title': 'emp_title',
                'home_ownership': 'home_ownership',
                'verification_status': 'verification_status',
                'fico_range_low': 'fico_range_low',
                'fico_range_high': 'fico_range_high',
                'delinq_2yrs': 'delinq_2yrs',
                'inq_last_6mths': 'inq_last_6mths',
                'pub_rec': 'pub_rec',
                'revol_bal': 'revol_bal',
                'revol_util': 'revol_util',
                'purpose': 'purpose',
                'title': 'title',
                'addr_state': 'addr_state',
                'loan_status': 'loan_status'
            }

            for loan_field, csv_field in optional_mappings.items():
                if csv_field in row.index and pd.notna(row[csv_field]):
                    loan_data_dict[loan_field] = row[csv_field]

            # Create LoanData object
            return LoanData(**loan_data_dict)

        except Exception as e:
            # Skip problematic rows
            return None

    def create_train_test_split(self,
                                loan_data_list: List[LoanData],
                                test_size: float = 0.2,
                                random_state: int = 42) -> Tuple[List[LoanData], List[LoanData], List[bool], List[bool]]:
        """
        Split loan data into train and test sets.

        Args:
            loan_data_list: List of loan data
            test_size: Proportion of data for testing
            random_state: Random seed for reproducibility

        Returns:
            Tuple of (train_data, test_data, train_labels, test_labels)
        """
        # Filter loans with known status
        loans_with_status = [
            loan for loan in loan_data_list if loan.loan_status is not None]

        if not loans_with_status:
            raise ValueError("No loans with status information found")

        # Create labels
        labels = [loan.is_good_loan() for loan in loans_with_status]

        # Shuffle data
        np.random.seed(random_state)
        indices = np.random.permutation(len(loans_with_status))

        # Split indices
        split_idx = int(len(indices) * (1 - test_size))
        train_indices = indices[:split_idx]
        test_indices = indices[split_idx:]

        # Create splits
        train_data = [loans_with_status[i] for i in train_indices]
        test_data = [loans_with_status[i] for i in test_indices]
        train_labels = [labels[i] for i in train_indices]
        test_labels = [labels[i] for i in test_indices]

        print(f"Train set: {len(train_data)} loans")
        print(f"Test set: {len(test_data)} loans")
        print(
            f"Train good loan ratio: {sum(train_labels)/len(train_labels):.3f}")
        print(f"Test good loan ratio: {sum(test_labels)/len(test_labels):.3f}")

        return train_data, test_data, train_labels, test_labels

    def get_data_summary(self, loan_data_list: List[LoanData]) -> Dict[str, Any]:
        """Get summary statistics of the loaded data."""
        if not loan_data_list:
            return {"message": "No data loaded"}

        # Basic statistics
        total_loans = len(loan_data_list)
        loans_with_status = [
            loan for loan in loan_data_list if loan.loan_status is not None]

        summary = {
            "total_loans": total_loans,
            "loans_with_status": len(loans_with_status),
            "data_completeness": len(loans_with_status) / total_loans if total_loans > 0 else 0
        }

        if loans_with_status:
            good_loans = sum(
                1 for loan in loans_with_status if loan.is_good_loan())
            summary.update({
                "good_loans": good_loans,
                "bad_loans": len(loans_with_status) - good_loans,
                "good_loan_ratio": good_loans / len(loans_with_status)
            })

            # Financial statistics
            loan_amounts = [loan.loan_amnt for loan in loans_with_status]
            annual_incomes = [loan.annual_inc for loan in loans_with_status]
            interest_rates = [loan.int_rate for loan in loans_with_status]

            summary.update({
                "avg_loan_amount": np.mean(loan_amounts),
                "avg_annual_income": np.mean(annual_incomes),
                "avg_interest_rate": np.mean(interest_rates),
                "median_loan_amount": np.median(loan_amounts),
                "median_annual_income": np.median(annual_incomes),
                "median_interest_rate": np.median(interest_rates)
            })

        return summary

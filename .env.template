# LLM API Keys - Copy this file to .env and fill in your actual API keys

# OpenAI API Key
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Key
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Google AI API Key
GOOGLE_API_KEY=your_google_api_key_here

# Groq API Key
GROQ_API_KEY=your_groq_api_key_here

# Hugging Face API Key (for PoLL approach)
HUGGINGFACE_API_KEY=your_huggingface_api_key_here

# Langfuse Configuration
LANGFUSE_SECRET_KEY=your_langfuse_secret_key_here
LANGFUSE_PUBLIC_KEY=your_langfuse_public_key_here
LANGFUSE_HOST=https://cloud.langfuse.com  # or your self-hosted instance

# Optional: Database connection for storing results
DATABASE_URL=sqlite:///loan_predictions.db

# Optional: Redis for caching
REDIS_URL=redis://localhost:6379/0

"""
Base classes for LLM loan approval approaches.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
import time
from datetime import datetime

from ..models.loan_data import LoanData
from ..models.llm_response import LLMResponse


class LoanApprovalApproach(ABC):
    """Abstract base class for loan approval approaches."""

    def __init__(self, name: str):
        self.name = name
        self.approach_type = self.__class__.__name__

    @abstractmethod
    def predict_single(self, loan_data: LoanData) -> LLMResponse:
        """
        Predict loan approval for a single loan application.

        Args:
            loan_data: Loan application data

        Returns:
            LLMResponse with prediction and reasoning
        """
        pass

    def predict_batch(self, loan_data_list: List[LoanData]) -> List[LLMResponse]:
        """
        Predict loan approval for a batch of loan applications.

        Args:
            loan_data_list: List of loan application data

        Returns:
            List of LLMResponse objects
        """
        responses = []

        for loan_data in loan_data_list:
            try:
                response = self.predict_single(loan_data)
                responses.append(response)
            except Exception as e:
                # Create error response
                error_response = LLMResponse(
                    decision="DENY",
                    confidence="VERY_LOW",
                    risk_assessment="VERY_HIGH",
                    reasoning=f"Error processing loan application: {str(e)}",
                    key_factors=["Processing Error"],
                    negative_factors=[f"System error: {str(e)}"],
                    model_name=self.name,
                    processing_time=0.0
                )
                responses.append(error_response)

        return responses

    def get_approach_info(self) -> Dict[str, Any]:
        """Get information about this approach."""
        return {
            "name": self.name,
            "type": self.approach_type,
            "description": self.__doc__ or "No description available"
        }


class PromptTemplate:
    """Template for generating loan evaluation prompts."""

    SYSTEM_PROMPT = """You are an expert loan underwriter with 20+ years of experience in credit risk assessment.
Your task is to evaluate loan applications and make approval/denial decisions based on the provided information.

You must respond in valid JSON format with the following structure:
{
  "decision": "APPROVE" | "DENY" | "CONDITIONAL",
  "confidence": "VERY_LOW" | "LOW" | "MEDIUM" | "HIGH" | "VERY_HIGH",
  "risk_assessment": "VERY_LOW" | "LOW" | "MEDIUM" | "HIGH" | "VERY_HIGH",
  "reasoning": "Detailed reasoning for your decision (2-3 paragraphs)",
  "key_factors": ["factor1", "factor2", "factor3"],
  "positive_factors": ["positive1", "positive2"],
  "negative_factors": ["negative1", "negative2"],
  "approval_probability": 0.0-1.0,
  "default_probability": 0.0-1.0
}

Consider these key factors in your evaluation:
- Credit score (FICO) and credit history
- Debt-to-income ratio
- Annual income and employment stability
- Loan amount and purpose
- Interest rate and loan grade
- Payment history and delinquencies
- Overall financial profile and risk indicators

Be thorough, objective, and explain your reasoning clearly. Always respond with valid JSON only."""

    LOAN_EVALUATION_TEMPLATE = """
Please evaluate the following loan application and provide your decision in JSON format:

{loan_context}

Analyze this loan application thoroughly and respond with a valid JSON object containing your evaluation. Be specific and reference the actual numbers from the application in your reasoning.

Ensure your response is valid JSON that can be parsed programmatically.
"""

    PROS_ANALYSIS_TEMPLATE = """
You are a loan advocate whose job is to identify and present the strongest arguments FOR approving this loan application.

{loan_context}

Please provide a comprehensive analysis of all POSITIVE factors that support loan approval:

1. **STRENGTHS**: Identify all positive aspects of this application
2. **RISK_MITIGATION**: Explain how positive factors mitigate potential risks
3. **APPROVAL_ARGUMENTS**: Present the strongest case for why this loan should be approved
4. **SUPPORTING_EVIDENCE**: Reference specific data points that support approval

Focus only on positive aspects and make the strongest possible case for loan approval.
"""

    CONS_ANALYSIS_TEMPLATE = """
You are a risk analyst whose job is to identify and present all potential concerns and arguments AGAINST approving this loan application.

{loan_context}

Please provide a comprehensive analysis of all NEGATIVE factors and risks:

1. **RISK_FACTORS**: Identify all potential risks and concerns
2. **RED_FLAGS**: Highlight any warning signs in the application
3. **DENIAL_ARGUMENTS**: Present the strongest case for why this loan should be denied
4. **RISK_QUANTIFICATION**: Explain the potential financial impact of these risks

Focus only on negative aspects and potential risks. Be thorough in identifying all concerns.
"""

    JUDGE_TEMPLATE = """
You are the final decision maker in a loan approval process. You have received analyses from both a loan advocate (presenting positive factors) and a risk analyst (presenting concerns).

{loan_context}

**ADVOCATE'S ANALYSIS (Positive Factors):**
{pros_analysis}

**RISK ANALYST'S ANALYSIS (Concerns):**
{cons_analysis}

Based on the loan application data and both analyses, please make your final decision:

1. **FINAL_DECISION**: [APPROVE/DENY/CONDITIONAL]
2. **CONFIDENCE**: [VERY_LOW/LOW/MEDIUM/HIGH/VERY_HIGH]
3. **RISK_ASSESSMENT**: [VERY_LOW/LOW/MEDIUM/HIGH/VERY_HIGH]
4. **JUDICIAL_REASONING**: Explain how you weighed the positive factors against the risks
5. **KEY_FACTORS**: List the most critical factors in your final decision
6. **DECISION_RATIONALE**: Provide detailed justification for your decision
7. **CONDITIONS**: If CONDITIONAL, specify what conditions must be met

Consider both perspectives carefully and make a balanced, well-reasoned decision.
"""

    @classmethod
    def format_loan_evaluation(cls, loan_data: LoanData) -> str:
        """Format loan evaluation prompt."""
        return cls.LOAN_EVALUATION_TEMPLATE.format(
            loan_context=loan_data.to_prompt_context()
        )

    @classmethod
    def format_pros_analysis(cls, loan_data: LoanData) -> str:
        """Format pros analysis prompt."""
        return cls.PROS_ANALYSIS_TEMPLATE.format(
            loan_context=loan_data.to_prompt_context()
        )

    @classmethod
    def format_cons_analysis(cls, loan_data: LoanData) -> str:
        """Format cons analysis prompt."""
        return cls.CONS_ANALYSIS_TEMPLATE.format(
            loan_context=loan_data.to_prompt_context()
        )

    @classmethod
    def format_judge_decision(cls, loan_data: LoanData, pros_analysis: str, cons_analysis: str) -> str:
        """Format judge decision prompt."""
        return cls.JUDGE_TEMPLATE.format(
            loan_context=loan_data.to_prompt_context(),
            pros_analysis=pros_analysis,
            cons_analysis=cons_analysis
        )

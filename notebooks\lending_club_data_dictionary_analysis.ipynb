import pandas as pd
import numpy as np
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Set display options for better readability
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', 50)

print("Libraries imported successfully!")
print(f"Pandas version: {pd.__version__}")
print(f"NumPy version: {np.__version__}")

# Define file paths
data_dir = Path("D:\Dhia\ING INFO 2\Stage d'ete\Proxym\Loan Request Approval Prediction\data\Lending Club loan data")
dict_file = data_dir / "LCDataDictionary.xlsx"  # Using the main file, not the ~$ temporary file
loan_file = data_dir / "loan.csv"

# Verify files exist
print(f"Data dictionary file exists: {dict_file.exists()}")
print(f"Loan data file exists: {loan_file.exists()}")
print(f"Loan file size: {loan_file.stat().st_size / (1024**3):.2f} GB")

import openpyxl

wb = openpyxl.load_workbook(dict_file)

if "~$" in dict_file.name:
    print("Temporary ~$ file detected. Please use the main Excel file for analysis.")
    exit()

if sheet_names := wb.sheetnames:
    print(f"Found {len(sheet_names)} sheets in the data dictionary:")
    for i, sheet in enumerate(sheet_names, 1):
        print(f"{i}. {sheet}")

    dict_sheets = {}
    # Convert each sheet to a pandas DataFrame
    for sheet in sheet_names:
        dict_sheets[sheet] = pd.read_excel(dict_file, sheet_name=sheet)
        print(f"Sheet '{sheet}': {dict_sheets[sheet].shape[0]} rows, {dict_sheets[sheet].shape[1]} columns")
else:
    print("No sheets found in the data dictionary.")


# Extract first column from each sheet (assuming it contains field names)
sheet_columns = {}

for sheet_name, df in dict_sheets.items():
    print(f"\n" + "="*30 + "Sheet: {sheet_name}" + "="*30)
    print(f"Shape: {df.shape}")
    print(f"Column names: {list(df.columns)}")
    
    print("\nFirst 3 rows:")
    print(df.head(3))
    
    # Extract key value data (first column)
    first_col = df.iloc[:, 0].dropna().tolist()
    sheet_columns[sheet_name] = first_col
    print(f"\nExtracted {len(first_col)} field names from first column")
    print(f"Sample field names: {first_col[:5]}")

from pprint import pprint
pprint(sheet_columns)

# Analyze sheet purposes and create summary
sheet_analysis = {}

for sheet_name, columns in sheet_columns.items():
    analysis = {
        'column_count': len(columns),
        'sample_columns': columns[:10],  # First 10 columns as sample
        'unique_columns': len(set(columns)),
        'has_duplicates': len(columns) != len(set(columns))
    }
    
    # Analyze column patterns to infer sheet purpose
    column_str = ' '.join(columns).lower()
    
    if 'current' in sheet_name.lower():
        analysis['likely_purpose'] = "Current/Active loan data fields"
    elif 'historical' in sheet_name.lower() or 'archive' in sheet_name.lower():
        analysis['likely_purpose'] = "Historical/Archived loan data fields"
    elif 'rejected' in sheet_name.lower():
        analysis['likely_purpose'] = "Rejected loan application fields"
    elif 'browse' in sheet_name.lower():
        analysis['likely_purpose'] = "Browsable/Public loan data fields"
    else:
        # Analyze content patterns
        if any(term in column_str for term in ['payment', 'installment', 'balance']):
            analysis['likely_purpose'] = "Payment and balance related fields"
        elif any(term in column_str for term in ['application', 'request', 'initial']):
            analysis['likely_purpose'] = "Loan application/request fields"
        else:
            analysis['likely_purpose'] = "General loan data fields"
    
    sheet_analysis[sheet_name] = analysis

# Display analysis results
print("=== SHEET ANALYSIS SUMMARY ===")
for sheet_name, analysis in sheet_analysis.items():
    print(f"\n📊 Sheet: {sheet_name}")
    print(f"   Purpose: {analysis['likely_purpose']}")
    print(f"   Columns: {analysis['column_count']} total, {analysis['unique_columns']} unique")
    print(f"   Duplicates: {'Yes' if analysis['has_duplicates'] else 'No'}")
    print(f"   Sample: {', '.join(analysis['sample_columns'][:5])}...")

# First, let's examine the file structure without loading the entire dataset
print("Examining loan.csv structure...")

# Read just the first few rows to understand structure
sample_df = pd.read_csv(loan_file, nrows=5)
print(f"\nSample data shape: {sample_df.shape}")
print(f"\nColumn names ({len(sample_df.columns)} total):")
for i, col in enumerate(sample_df.columns, 1):
    print(f"{i:3d}. {col}")

print("\nFirst 3 rows:")
print(sample_df.head(3))

# Get total row count efficiently
print("Counting total rows in loan.csv...")
row_count = sum(1 for line in open(loan_file, 'r', encoding='utf-8')) - 1  # -1 for header
print(f"Total rows in loan.csv: {row_count:,}")

# Load column names only (more memory efficient)
loan_columns = list(pd.read_csv(loan_file, nrows=0).columns)
print(f"\nTotal columns in loan.csv: {len(loan_columns)}")

# Store loan columns for comparison
loan_column_set = set(loan_columns)
print(f"Unique columns in loan dataset: {len(loan_column_set)}")

# Compare each sheet with actual loan data
comparison_results = {}

for sheet_name, dict_columns in sheet_columns.items():
    dict_column_set = set(dict_columns)
    
    # Find matches and missing columns
    present_columns = dict_column_set.intersection(loan_column_set)
    missing_columns = dict_column_set - loan_column_set
    
    # Calculate percentages
    total_dict_cols = len(dict_column_set)
    present_count = len(present_columns)
    missing_count = len(missing_columns)
    
    present_pct = (present_count / total_dict_cols * 100) if total_dict_cols > 0 else 0
    missing_pct = (missing_count / total_dict_cols * 100) if total_dict_cols > 0 else 0
    
    comparison_results[sheet_name] = {
        'total_dict_columns': total_dict_cols,
        'present_columns': present_columns,
        'missing_columns': missing_columns,
        'present_count': present_count,
        'missing_count': missing_count,
        'present_percentage': present_pct,
        'missing_percentage': missing_pct
    }

print("=== COMPARISON SUMMARY ===")
for sheet_name, results in comparison_results.items():
    print(f"\n📋 Sheet: {sheet_name}")
    print(f"   Total columns in dictionary: {results['total_dict_columns']}")
    print(f"   Present in loan.csv: {results['present_count']} ({results['present_percentage']:.1f}%)")
    print(f"   Missing from loan.csv: {results['missing_count']} ({results['missing_percentage']:.1f}%)")

# Analyze missing columns patterns
all_missing_columns = set()
for results in comparison_results.values():
    all_missing_columns.update(results['missing_columns'])

print(f"=== MISSING COLUMNS ANALYSIS ===")
print(f"Total unique missing columns across all sheets: {len(all_missing_columns)}")

# Categorize missing columns by likely reasons
missing_categories = {
    'Privacy/Sensitive': [],
    'Deprecated/Legacy': [],
    'Internal/System': [],
    'Time-specific': [],
    'Other': []
}

for col in all_missing_columns:
    col_lower = col.lower()
    
    # Privacy/Sensitive data
    if any(term in col_lower for term in ['ssn', 'social', 'address', 'phone', 'email', 'name', 'id']):
        missing_categories['Privacy/Sensitive'].append(col)
    # Deprecated/Legacy fields
    elif any(term in col_lower for term in ['old', 'legacy', 'deprecated', 'former', 'previous']):
        missing_categories['Deprecated/Legacy'].append(col)
    # Internal/System fields
    elif any(term in col_lower for term in ['internal', 'system', 'admin', 'process', 'workflow']):
        missing_categories['Internal/System'].append(col)
    # Time-specific fields
    elif any(term in col_lower for term in ['2007', '2008', '2009', '2010', '2011', '2012', 'quarter', 'q1', 'q2', 'q3', 'q4']):
        missing_categories['Time-specific'].append(col)
    else:
        missing_categories['Other'].append(col)

# Display categorized missing columns
for category, columns in missing_categories.items():
    if columns:
        print(f"\n🔍 {category} ({len(columns)} columns):")
        for col in sorted(columns):
            print(f"   - {col}")

# Create summary DataFrame for sheet comparison
summary_data = []
for sheet_name, results in comparison_results.items():
    summary_data.append({
        'Sheet Name': sheet_name,
        'Purpose': sheet_analysis[sheet_name]['likely_purpose'],
        'Total Columns': results['total_dict_columns'],
        'Present in loan.csv': results['present_count'],
        'Missing from loan.csv': results['missing_count'],
        'Present %': f"{results['present_percentage']:.1f}%",
        'Missing %': f"{results['missing_percentage']:.1f}%"
    })

summary_df = pd.DataFrame(summary_data)
print("=== COMPREHENSIVE SUMMARY TABLE ===")
print(summary_df.to_string(index=False))

total_dict_cols = sum(results['total_dict_columns'] for results in comparison_results.values())
total_present = sum(results['present_count'] for results in comparison_results.values())
total_missing = sum(results['missing_count'] for results in comparison_results.values())

print(f"\n=== OVERALL STATISTICS ===")
print(f"Total columns across all dictionary sheets: {total_dict_cols}")
print(f"Total present in loan.csv: {total_present}")
print(f"Total missing from loan.csv: {total_missing}")
print(f"Overall match rate: {(total_present/total_dict_cols*100):.1f}%")

all_dict_columns = list()
for columns in sheet_columns.values():
    # Flatten the list of lists
    all_dict_columns += columns
all_dict_columns

df_columns = pd.Series(all_dict_columns)
duplicate_columns = df_columns[df_columns.duplicated()].tolist()
duplicate_columns

print(f"Combined sheets keys: {len(all_dict_columns)}")
print(f"Unique combined sheets keys: {len(set(all_dict_columns))}")
print(f"Duplicated sheets keys: {len(duplicate_columns)}")
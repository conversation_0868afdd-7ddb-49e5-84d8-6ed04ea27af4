"""
Groq provider implementation.
"""

import os
from typing import Any, Dict
import groq
from langchain_core.messages import HumanMessage

from .base import <PERSON><PERSON><PERSON><PERSON>, LLMConfig


class GroqProvider(LLMProvider):
    """Groq provider for fast inference."""

    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.api_key = config.api_key or os.getenv("GROQ_API_KEY")
        if not self.api_key:
            raise ValueError(
                "Groq API key not found. Set GROQ_API_KEY environment variable.")

    def _initialize_client(self) -> groq.Groq:
        """Initialize the Groq client."""
        return groq.Groq(api_key=self.api_key)

    def _generate_response(self, prompt: str, **kwargs) -> str:
        """Generate response from Groq."""
        client = self.get_client()

        # Prepare parameters
        temperature = kwargs.get("temperature", self.config.temperature)
        max_tokens = kwargs.get("max_tokens", self.config.max_tokens)

        try:
            # Create chat completion
            response = client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                temperature=temperature,
                max_tokens=max_tokens,
                **self.config.additional_params
            )

            return response.choices[0].message.content
        except Exception as e:
            raise Exception(f"Groq API error: {str(e)}")

    def validate_config(self) -> bool:
        """Validate Groq configuration."""
        if not self.api_key:
            return False

        # Check if model name is valid for Groq
        valid_models = [
            "llama3-70b-8192",
            "llama3-8b-8192",
            "mixtral-8x7b-32768",
            "gemma-7b-it",
            "gemma2-9b-it"
        ]

        return self.model_name in valid_models

    def get_model_info(self) -> Dict[str, Any]:
        """Get Groq model information."""
        info = super().get_model_info()
        info.update({
            "api_key_configured": bool(self.api_key),
            "supported_features": ["text_generation", "conversation", "fast_inference"]
        })
        return info

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.impute import SimpleImputer, KNNImputer
from sklearn.feature_selection import SelectKBest, chi2, f_classif
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
import pathlib as pl
import warnings
warnings.filterwarnings('ignore')

# Set display options
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
plt.style.use('seaborn-v0_8')

# Make sure the platform architecture is x64 to allow the use of more than 4Gb of RAM
import platform
print(platform.architecture())

%%time

# Load data efficiently for large dataset
def load_loan_data(file_path, chunk_size=10000):
    """
    Load large CSV file in chunks to handle memory efficiently
    """
    chunks = []
    for chunk in pd.read_csv(file_path, chunksize=chunk_size):
        chunks.append(chunk)
    return pd.concat(chunks, ignore_index=True)

# Load the dataset
# DATA_PATH = "../data/Lending Club loan data/loan.csv"
DATA_PATH = pl.Path.cwd() / "data" / "Lending Club loan data" / "loan.csv"
print("Loading loan data...")
df = load_loan_data(DATA_PATH)
print(f"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
df.shape

# Initial data assessment
def assess_data_quality(df):
    """
    Comprehensive data quality assessment
    """
    print("=" * 30)
    print("DATA QUALITY ASSESSMENT")
    print("=" * 30)
    
    print(f"Dataset shape: {df.shape}")
    
    
    # Missing values analysis
    missing_data = pd.DataFrame({
        'Column': df.columns,
        'Missing_Count': df.isnull().sum(),
        'Missing_Percentage': (df.isnull().sum() / len(df)) * 100,
        'Data_Type': df.dtypes
    })
    
    missing_data = missing_data[missing_data['Missing_Count'] > 0].sort_values('Missing_Percentage', ascending=False)
    
    print(f"\nColumns with missing values: {len(missing_data)}")
    print("\nColumns Missing data analysis sorted by missing percentage:")
    print(missing_data.to_string())
    
    print("+" + "="*60 + "+")

    n_float_dtype = missing_data[missing_data['Data_Type'] != "float64"]
    
    print(f"\nColumns with non-float data type: {len(n_float_dtype)}")
    print("\nColumns Missing data analysis sorted by missing percentage:")
    print(n_float_dtype.to_string())

    return missing_data

missing_analysis = assess_data_quality(df)

import pandas as pd
import matplotlib.pyplot as plt

def visualize_missing_data(df, columns_to_include=None):
    """
    Visualize missing data statistics in a DataFrame.
    """
    if columns_to_include is not None and len(columns_to_include) > 0:
        # Filter the df to only include the specified columns
        # Ensure the specified columns actually exist in the missing_df
        valid_included_cols = [col for col in columns_to_include if col in df['Column'].values]
        
        if valid_included_cols:
            # Replace df with filtered version
            df = df[df['Column'].isin(valid_included_cols)]
        else:
            print("Warning: None of the specified columns to include have missing data or exist in the DataFrame.")
            print("Drawing all columns with missing data.")
            
    # the number of rows of the df correspondes to the number of columns
    num_cols_to_plot = len(df) 

    # Adjust the figure height based on the number of columns to plot
    fig_height = max(6, min(24, num_cols_to_plot * 0.3))
    plt.figure(figsize=(12, fig_height))

    plt.barh(df['Column'], df['Missing_Percentage'], color='skyblue')
    plt.xlabel('Missing Percentage')
    plt.ylabel('Columns')
    plt.title('Missing Data Statistics')
    plt.gca().invert_yaxis() 
    plt.tight_layout()
    plt.show()

visualize_missing_data(missing_analysis)

df[df["hardship_reason"].notna()][["hardship_reason", "hardship_type", "hardship_status"]].head()

df["loan_status"].value_counts()

df_status_counts = pd.DataFrame(df['loan_status'].value_counts())
status_counts = df['loan_status'].value_counts()

status_counts.values

import matplotlib.ticker as mticker


# Analyze loan_status (target variable)
def analyze_target_variable(df, target_col='loan_status', target_name='Target variable', scientific_notation=False):
    """
    Analyze the target variable's value distribution
    """
    print("=" * 60)
    print("TARGET VARIABLE ANALYSIS - LOAN STATUS")
    print("=" * 60)
    
    status_counts = df[target_col].value_counts()
    # Calculate distribution of each unique value
    status_pct = df[target_col].value_counts(normalize=True) * 100
    
    print("Loan Status Distribution:")
    for status, count in status_counts.items():
        pct = status_pct[status]
        print(f"{status}: {count:,} ({pct:.2f}%)")
    
    # Create binary target for approval prediction
    # Good loans: 'Fully Paid', 'Current' -> 1
    # Bad loans: 'Charged Off', 'Default', 'Late', etc. -> 0
    good_loans = ['Fully Paid', 'Current']
    df['loan_approved'] = df[target_col].apply(lambda x: 1 if x in good_loans else 0)
    
    approval_rate = df['loan_approved'].mean() * 100
    print(f"\nOverall Approval Rate: {approval_rate:.2f}%")
    
    # Visualization
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Original status distribution
    # status_counts.plot(kind='bar', ax=ax1, color='skyblue')
    # After creating your plot and before plt.show():
    ax1.barh(status_counts.index, status_counts.values, color='skyblue')
    if not scientific_notation:
        ax1.xaxis.set_major_formatter(mticker.ScalarFormatter(useOffset=False, useMathText=False))
        ax1.ticklabel_format(style='plain', axis='x')
    ax1.set_title(target_name + ' Distribution')
    ax1.set_xlabel('Count')
    ax1.set_ylabel('Loan Status')
    ax1.tick_params(axis='x', rotation=60)
    ax1.invert_yaxis() 
    
    # Binary approval distribution
    approval_counts = df['loan_approved'].value_counts()
    labels = ['Approved/Good', 'Not Approved/Defaulted']
    ax2.pie(approval_counts.values, labels=labels, autopct='%1.1f%%', startangle=90)
    ax2.set_title('Binary Loan Approval Distribution')
    
    plt.tight_layout()
    plt.show()
    
    return df

df = analyze_target_variable(df, target_name='Loan Status')

CRITICAL_FEATURES = [
    'loan_amnt', 'int_rate', 'grade', 'sub_grade', 'annual_inc', 'dti',
    'fico_range_low', 'fico_range_high', 'emp_length', 'home_ownership',
    'verification_status', 'purpose', 'addr_state', 'delinq_2yrs',
    'inq_last_6mths', 'open_acc', 'pub_rec', 'revol_bal', 'revol_util',
    'total_acc', 'collections_12_mths_ex_med', 'acc_now_delinq'
]

visualize_missing_data(missing_analysis, columns_to_include=CRITICAL_FEATURES)

def handle_missing_values(df, target_col='loan_approved'):
    """
    Cleans the input DataFrame by handling missing values for a set of critical features.
    - Drops any critical feature columns that are entirely empty.
    - Imputes missing values in numeric columns with the median.
    - Imputes missing values in categorical columns with 'Unknown'.
    - Provides detailed logs about dropped and imputed columns.
    Returns the cleaned DataFrame and the list of features actually included.
    """
    print("=" * 60)
    print("FEATURE IMPORTANCE ANALYSIS PREPROCESSING")
    print("=" * 60)
    
    # Filter available critical features from whole dataframe
    available_features = [col for col in CRITICAL_FEATURES if col in df.columns]
    print(f"Initial critical features found in DataFrame: {len(available_features)} out of {len(CRITICAL_FEATURES)}")
    
    analysis_df = df[available_features + [target_col]].copy()

    # Delete entirely empty columns (NEW IMPROVEMENT)
    columns_to_drop_due_to_empty = []
    for col in analysis_df.columns:
        if col != target_col and analysis_df[col].isnull().all():
            columns_to_drop_due_to_empty.append(col)

    for critical_column in CRITICAL_FEATURES:
        if critical_column in columns_to_drop_due_to_empty:
            print("WARNING: Critical feature", critical_column, "is entirely empty and will be dropped.")
            print("Please update the CRITICAL_FEATURES list.")

    if columns_to_drop_due_to_empty:
        print(f"Dropping {len(columns_to_drop_due_to_empty)} features that are entirely empty: {columns_to_drop_due_to_empty}")
        analysis_df.drop(columns=columns_to_drop_due_to_empty, inplace=True)
        # Update our list of available features to reflect the dropped columns
        available_features = [col for col in available_features if col not in columns_to_drop_due_to_empty]
    else:
        print("No entirely empty features found to drop.")
    
    # Handle remaining missing values (Imputation)

    # Separate columns into numeric and categorical types for appropriate imputation.
    numeric_cols = analysis_df.select_dtypes(include=[np.number]).columns
    categorical_cols = analysis_df.select_dtypes(include=['object']).columns
    
    print("+" + "="*30 + "+")
    print("Imputing numerical columns")
    print("+" + "="*30 + "+")

    # Numeric columns -> Fill with median.
    # The median is preferred over the mean: less sensitive to outliers.
    total_imputed_numeric_count = 0
    for col in numeric_cols:
        imputed_numeric_count = 0
        if col != target_col: # Again, don't impute the target variable
            if analysis_df[col].isnull().any(): # Only attempt imputation if there are actual NaNs
                analysis_df[col].fillna(analysis_df[col].median(), inplace=True)
                imputed_numeric_count += 1
                total_imputed_numeric_count += 1
        if imputed_numeric_count > 0:
            print(f"Imputed missing numeric values with median in {imputed_numeric_count} columns for {col}.")
        else:
            print(f"No numeric columns required imputation for {col}.")

    if total_imputed_numeric_count > 0:
        print(f"Imputed missing numeric values with median in {total_imputed_numeric_count} columns in Total.")
    else:
        print("No numeric columns required imputation.")

    print("+" + "="*30 + "+")
    print("Imputing categorical columns")
    print("+" + "="*30 + "+")

    # Categorical columns -> Fill with mode or 'Unknown'.
    # The mode is the most frequent category. 'Unknown' is a fallback for very rare cases
    # where even after dropping entirely empty columns, a categorical column might somehow have no mode.
    total_imputed_categorical_count = 0
    for col in categorical_cols:
        imputed_categorical_count = 0
        if analysis_df[col].isnull().any(): # Only attempt imputation if there are actual NaNs
            analysis_df[col].fillna('Unknown', inplace=True)
            imputed_categorical_count += 1
            total_imputed_categorical_count += 1
        if imputed_categorical_count > 0:
            print(f"Imputed missing categorical values with mode/Unknown in {imputed_categorical_count} columns for {col}.")
        else:
            print(f"No categorical columns required imputation for {col}.")

    if total_imputed_categorical_count > 0:
        print(f"Imputed missing categorical values with mode/Unknown in {total_imputed_categorical_count} columns.")
    else:
        print("No categorical columns required imputation.")

    # imputed_categorical_count = 0
    # for col in categorical_cols:
    #     if analysis_df[col].isnull().any(): # Only attempt imputation if there are actual NaNs
    #         mode_val = analysis_df[col].mode()
    #         if not mode_val.empty:
    #             analysis_df[col].fillna(mode_val[0], inplace=True)
    #         else:
    #             # Fallback if mode is empty (highly unlikely after dropping all-NaN columns, but robust)
    #             analysis_df[col].fillna('Unknown', inplace=True) 
    #         imputed_categorical_count += 1
    # if imputed_categorical_count > 0:
    #     print(f"Imputed missing categorical values with mode/Unknown in {imputed_categorical_count} columns.")
    # else:
    #     print("No categorical columns required imputation.")

    print(f"\nFinal features remaining for analysis: {len(available_features)}.")
    print("-" * 60)
    
    # The function returns the cleaned DataFrame ready for feature importance analysis,
    # and the updated list of feature names actually included.
    return analysis_df, available_features

analysis_df, available_features = handle_missing_values(df)

SAVE_DATA_PATH = pl.Path.cwd() / "data" / "Lending Club loan data" / "filtered_loan_data.csv"
df.to_csv(SAVE_DATA_PATH, index=False)

def comprehensive_preprocessing(df):
    """
    Comprehensive preprocessing pipeline for loan data
    """
    print("=" * 60)
    print("COMPREHENSIVE PREPROCESSING PIPELINE")
    print("=" * 60)
    
    df_processed = df.copy()
    
    # Step 1: Remove columns with >70% missing values
    high_missing_cols = []
    for col in df_processed.columns:
        missing_pct = (df_processed[col].isnull().sum() / len(df_processed)) * 100
        if missing_pct > 70:
            high_missing_cols.append(col)
    
    print(f"Removing {len(high_missing_cols)} columns with >70% missing values")
    df_processed = df_processed.drop(columns=high_missing_cols)
    
    # Step 2: Handle date columns
    date_columns = ['issue_d', 'earliest_cr_line', 'last_pymnt_d', 'last_credit_pull_d']
    for col in date_columns:
        if col in df_processed.columns:
            df_processed[col] = pd.to_datetime(df_processed[col], errors='coerce')
            # Extract useful features from dates
            if col == 'issue_d':
                df_processed['issue_year'] = df_processed[col].dt.year
                df_processed['issue_month'] = df_processed[col].dt.month
            elif col == 'earliest_cr_line':
                df_processed['credit_history_length'] = (pd.to_datetime('2018-12-31') - df_processed[col]).dt.days / 365.25
    
    # Step 3: Handle employment length
    if 'emp_length' in df_processed.columns:
        def parse_emp_length(emp_length):
            if pd.isna(emp_length) or emp_length == 'n/a':
                return 0
            elif '< 1 year' in str(emp_length):
                return 0.5
            elif '10+ years' in str(emp_length):
                return 10
            else:
                try:
                    return float(str(emp_length).split()[0])
                except:
                    return 0
        
        df_processed['emp_length_numeric'] = df_processed['emp_length'].apply(parse_emp_length)
    
    # Step 4: Create derived features
    if 'annual_inc' in df_processed.columns and 'loan_amnt' in df_processed.columns:
        df_processed['loan_to_income_ratio'] = df_processed['loan_amnt'] / (df_processed['annual_inc'] + 1)
    
    if 'fico_range_low' in df_processed.columns and 'fico_range_high' in df_processed.columns:
        df_processed['fico_avg'] = (df_processed['fico_range_low'] + df_processed['fico_range_high']) / 2
    
    if 'revol_bal' in df_processed.columns and 'annual_inc' in df_processed.columns:
        df_processed['revol_bal_to_income'] = df_processed['revol_bal'] / (df_processed['annual_inc'] + 1)
    
    print(f"Processed dataset shape: {df_processed.shape}")
    return df_processed

df_processed = comprehensive_preprocessing(df)

def handle_missing_values(df, strategy='mixed'):
    """
    Handle missing values with different strategies based on feature importance
    """
    print("=" * 60)
    print("MISSING VALUE TREATMENT")
    print("=" * 60)
    
    df_clean = df.copy()
    
    # Separate numeric and categorical columns
    numeric_cols = df_clean.select_dtypes(include=[np.number]).columns.tolist()
    categorical_cols = df_clean.select_dtypes(include=['object']).columns.tolist()
    
    # Remove target column from processing
    if 'loan_approved' in numeric_cols:
        numeric_cols.remove('loan_approved')
    
    print(f"Numeric columns: {len(numeric_cols)}")
    print(f"Categorical columns: {len(categorical_cols)}")
    
    # Handle numeric missing values
    for col in numeric_cols:
        missing_pct = (df_clean[col].isnull().sum() / len(df_clean)) * 100
        if missing_pct > 0:
            if col in ['annual_inc', 'dti', 'revol_util']:  # Important financial metrics
                # Use median for financial metrics
                df_clean[col].fillna(df_clean[col].median(), inplace=True)
            elif col in ['delinq_2yrs', 'inq_last_6mths', 'pub_rec', 'collections_12_mths_ex_med']:
                # Use 0 for count-based metrics (assuming no record means 0)
                df_clean[col].fillna(0, inplace=True)
            else:
                # Use median for other numeric columns
                df_clean[col].fillna(df_clean[col].median(), inplace=True)
    
    # Handle categorical missing values
    for col in categorical_cols:
        missing_pct = (df_clean[col].isnull().sum() / len(df_clean)) * 100
        if missing_pct > 0:
            if col in ['grade', 'sub_grade']:  # Critical categorical features
                # Use mode for grade-related features
                mode_val = df_clean[col].mode()[0] if not df_clean[col].mode().empty else 'Unknown'
                df_clean[col].fillna(mode_val, inplace=True)
            else:
                # Use 'Unknown' for other categorical features
                df_clean[col].fillna('Unknown', inplace=True)
    
    # Report missing values after treatment
    remaining_missing = df_clean.isnull().sum().sum()
    print(f"Remaining missing values: {remaining_missing}")
    
    return df_clean

df_clean = handle_missing_values(df_processed)

def encode_categorical_features(df, target_col='loan_approved'):
    """
    Encode categorical features for machine learning
    """
    print("=" * 60)
    print("CATEGORICAL FEATURE ENCODING")
    print("=" * 60)
    
    df_encoded = df.copy()
    
    # High cardinality categorical features (use target encoding or frequency encoding)
    high_cardinality_cols = ['addr_state', 'emp_title']
    
    # Medium cardinality categorical features (use one-hot encoding)
    medium_cardinality_cols = ['grade', 'sub_grade', 'home_ownership', 'verification_status', 'purpose']
    
    # Handle high cardinality features with frequency encoding
    for col in high_cardinality_cols:
        if col in df_encoded.columns:
            freq_map = df_encoded[col].value_counts().to_dict()
            df_encoded[f'{col}_frequency'] = df_encoded[col].map(freq_map)
            
            # Target encoding for high cardinality
            if target_col in df_encoded.columns:
                target_mean = df_encoded.groupby(col)[target_col].mean()
                df_encoded[f'{col}_target_encoded'] = df_encoded[col].map(target_mean)
    
    # One-hot encode medium cardinality features
    for col in medium_cardinality_cols:
        if col in df_encoded.columns:
            # Limit to top categories to avoid too many features
            top_categories = df_encoded[col].value_counts().head(10).index.tolist()
            df_encoded[col] = df_encoded[col].apply(lambda x: x if x in top_categories else 'Other')
            
            # Create dummy variables
            dummies = pd.get_dummies(df_encoded[col], prefix=col, drop_first=True)
            df_encoded = pd.concat([df_encoded, dummies], axis=1)
    
    print(f"Encoded dataset shape: {df_encoded.shape}")
    return df_encoded

df_encoded = encode_categorical_features(df_clean)

def prepare_final_dataset(df, target_col='loan_approved'):
    """
    Final dataset preparation for machine learning
    """
    print("=" * 60)
    print("FINAL DATASET PREPARATION")
    print("=" * 60)
    
    # Separate features and target
    if target_col in df.columns:
        X = df.drop(columns=[target_col])
        y = df[target_col]
    else:
        X = df.copy()
        y = None
    
    # Remove non-predictive columns
    cols_to_remove = ['id', 'member_id', 'url', 'desc', 'title', 'loan_status']
    cols_to_remove = [col for col in cols_to_remove if col in X.columns]
    X = X.drop(columns=cols_to_remove)
    
    # Select only numeric columns for scaling
    numeric_cols = X.select_dtypes(include=[np.number]).columns.tolist()
    
    # Scale numeric features
    scaler = StandardScaler()
    X_scaled = X.copy()
    X_scaled[numeric_cols] = scaler.fit_transform(X[numeric_cols])
    
    print(f"Final feature matrix shape: {X_scaled.shape}")
    if y is not None:
        print(f"Target distribution: {y.value_counts().to_dict()}")
    
    return X_scaled, y, scaler

X_final, y_final, scaler = prepare_final_dataset(df_encoded)

def analyze_feature_importance_rf(X, y, top_n=20):
    """
    Analyze feature importance using Random Forest
    """
    print("=" * 60)
    print("RANDOM FOREST FEATURE IMPORTANCE ANALYSIS")
    print("=" * 60)
    
    if y is None:
        print("No target variable available for importance analysis")
        return None
    
    # Sample data if too large
    if len(X) > 50000:
        sample_idx = np.random.choice(len(X), 50000, replace=False)
        X_sample = X.iloc[sample_idx]
        y_sample = y.iloc[sample_idx]
    else:
        X_sample = X
        y_sample = y
    
    # Train Random Forest
    rf = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
    rf.fit(X_sample, y_sample)
    
    # Get feature importance
    importance_df = pd.DataFrame({
        'feature': X.columns,
        'importance': rf.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print(f"Top {top_n} Most Important Features:")
    print(importance_df.head(top_n))
    
    # Visualization
    plt.figure(figsize=(12, 8))
    top_features = importance_df.head(top_n)
    plt.barh(range(len(top_features)), top_features['importance'])
    plt.yticks(range(len(top_features)), top_features['feature'])
    plt.xlabel('Feature Importance')
    plt.title(f'Top {top_n} Feature Importance (Random Forest)')
    plt.gca().invert_yaxis()
    plt.tight_layout()
    plt.show()
    
    return importance_df

if y_final is not None:
    feature_importance = analyze_feature_importance_rf(X_final, y_final)

def generate_preprocessing_summary():
    """
    Generate comprehensive preprocessing summary and recommendations
    """
    print("=" * 80)
    print("PREPROCESSING SUMMARY & RECOMMENDATIONS")
    print("=" * 80)
    
    recommendations = """
    KEY PREPROCESSING STEPS COMPLETED:
    
    1. DATA QUALITY ASSESSMENT
       ✓ Identified columns with high missing values (>70%) and removed them
       ✓ Analyzed data types and mixed-type columns
       ✓ Assessed overall data quality and memory usage
    
    2. TARGET VARIABLE ENGINEERING
       ✓ Created binary target 'loan_approved' from loan_status
       ✓ Analyzed approval rates and loan outcomes
    
    3. FEATURE ENGINEERING
       ✓ Parsed employment length into numeric format
       ✓ Created derived features (loan-to-income ratio, FICO average)
       ✓ Extracted useful information from date columns
    
    4. MISSING VALUE TREATMENT
       ✓ Used domain-specific imputation strategies
       ✓ Applied median imputation for financial metrics
       ✓ Used zero imputation for count-based features
    
    5. CATEGORICAL ENCODING
       ✓ Applied frequency encoding for high-cardinality features
       ✓ Used one-hot encoding for medium-cardinality features
       ✓ Implemented target encoding where appropriate
    
    6. FEATURE SCALING
       ✓ Applied StandardScaler to numeric features
       ✓ Prepared final feature matrix for machine learning
    
    MOST IMPORTANT FEATURES FOR LOAN APPROVAL:
    
    PRIMARY IMPACT (Critical for Decision):
    • Interest Rate (int_rate) - Higher rates indicate higher risk
    • Loan Grade (grade/sub_grade) - LC's internal risk assessment
    • FICO Score (fico_range_low/high) - Credit worthiness
    • Debt-to-Income Ratio (dti) - Ability to repay
    • Annual Income (annual_inc) - Repayment capacity
    • Loan Amount (loan_amnt) - Risk exposure
    
    SECONDARY IMPACT (Important for Fine-tuning):
    • Employment Length (emp_length) - Stability indicator
    • Home Ownership (home_ownership) - Financial stability
    • Verification Status (verification_status) - Income verification
    • Purpose (purpose) - Loan intent and risk profile
    • Delinquencies (delinq_2yrs) - Past payment behavior
    • Credit Inquiries (inq_last_6mths) - Recent credit seeking
    
    NEXT STEPS FOR MODEL BUILDING:
    
    1. Split data into train/validation/test sets (70/15/15)
    2. Handle class imbalance if present (SMOTE, class weights)
    3. Try multiple algorithms (Random Forest, XGBoost, LightGBM)
    4. Perform hyperparameter tuning
    5. Evaluate using appropriate metrics (AUC-ROC, Precision-Recall)
    6. Implement cross-validation for robust evaluation
    7. Create feature importance plots and model interpretability
    
    BUSINESS IMPACT CONSIDERATIONS:
    
    • Focus on reducing False Positives (approving bad loans)
    • Balance approval rates with risk management
    • Consider regulatory compliance requirements
    • Implement model monitoring for drift detection
    • Ensure fairness across demographic groups
    """
    
    print(recommendations)

generate_preprocessing_summary()
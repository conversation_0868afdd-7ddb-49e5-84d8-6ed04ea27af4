{"cells": [{"cell_type": "code", "execution_count": null, "id": "b72c3d8f", "metadata": {}, "outputs": [], "source": ["dict(df[\"loan_amnt\"].value_counts())"]}, {"cell_type": "code", "execution_count": null, "id": "231bf3dc", "metadata": {}, "outputs": [], "source": ["dict(df[\"loan_amnt\"].value_counts())"]}, {"cell_type": "code", "execution_count": null, "id": "bd84215e", "metadata": {}, "outputs": [], "source": ["dict(df[\"loan_amnt\"].value_counts())"]}, {"cell_type": "code", "execution_count": null, "id": "630a0627", "metadata": {}, "outputs": [], "source": ["dict(df[\"loan_amnt\"].value_counts())"]}, {"cell_type": "code", "execution_count": null, "id": "fe5276fc", "metadata": {}, "outputs": [], "source": ["dict(df[\"loan_amnt\"].value_counts())"]}, {"cell_type": "code", "execution_count": null, "id": "3a3625ac", "metadata": {}, "outputs": [], "source": ["dict(df[\"loan_amnt\"].value_counts())"]}, {"cell_type": "code", "execution_count": null, "id": "a8b37e8b", "metadata": {}, "outputs": [], "source": ["dict(df[\"loan_amnt\"].value_counts())"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}
"""
PoLL (Plateau of LLMs) approach for loan approval prediction.
"""

import time
import statistics
from typing import Dict, Any, List, Optional
from datetime import datetime

from .base import LoanApprovalApproach, PromptTemplate
from ..models.loan_data import LoanData
from ..models.llm_response import PoLLResponse, LoanDecision, ConfidenceLevel, RiskLevel
from ..providers.huggingface_provider import HuggingFaceEnsemble


class PoLLApproach(LoanApprovalApproach):
    """
    PoLL (Plateau of LLMs) approach using multiple smaller financial-tuned models.
    
    This approach uses an ensemble of smaller, specialized models that are fine-tuned
    on financial data to make loan approval decisions through consensus.
    """
    
    def __init__(self, ensemble: HuggingFaceEnsemble, ensemble_method: str = "weighted_average", name: str = "PoLL Ensemble"):
        super().__init__(name)
        self.ensemble = ensemble
        self.ensemble_method = ensemble_method
        self.prompt_template = PromptTemplate()
    
    def predict_single(self, loan_data: LoanData) -> PoLLResponse:
        """
        Predict loan approval using PoLL ensemble approach.
        
        Args:
            loan_data: Loan application data
            
        Returns:
            PoLLResponse with ensemble prediction and individual model responses
        """
        start_time = time.time()
        
        try:
            # Create evaluation prompt
            prompt = self._create_evaluation_prompt(loan_data)
            
            # Get ensemble response
            ensemble_result = self.ensemble.generate_ensemble_response(prompt, self.ensemble_method)
            
            if not ensemble_result.get("success", False):
                raise Exception(f"Ensemble generation failed: {ensemble_result.get('error', 'Unknown error')}")
            
            # Parse individual responses
            individual_responses = ensemble_result.get("individual_responses", [])
            parsed_responses = []
            
            for response in individual_responses:
                if response.get("success", False):
                    parsed = self._parse_individual_response(response)
                    parsed_responses.append(parsed)
            
            # Combine responses
            combined_result = self._combine_responses(parsed_responses)
            
            # Calculate consensus metrics
            consensus_metrics = self._calculate_consensus_metrics(parsed_responses)
            
            processing_time = time.time() - start_time
            
            # Create PoLL response
            response = PoLLResponse(
                decision=combined_result["decision"],
                confidence=combined_result["confidence"],
                risk_assessment=combined_result["risk_assessment"],
                reasoning=combined_result["reasoning"],
                key_factors=combined_result["key_factors"],
                positive_factors=combined_result["positive_factors"],
                negative_factors=combined_result["negative_factors"],
                approval_probability=combined_result.get("approval_probability"),
                default_probability=combined_result.get("default_probability"),
                model_name=f"PoLL Ensemble ({len(parsed_responses)} models)",
                processing_time=processing_time,
                individual_responses=individual_responses,
                ensemble_method=self.ensemble_method,
                model_weights=self._get_model_weights(),
                consensus_score=consensus_metrics["consensus_score"],
                disagreement_factors=consensus_metrics["disagreement_factors"]
            )
            
            return response
            
        except Exception as e:
            processing_time = time.time() - start_time
            
            # Return error response
            return PoLLResponse(
                decision=LoanDecision.DENY,
                confidence=ConfidenceLevel.VERY_LOW,
                risk_assessment=RiskLevel.VERY_HIGH,
                reasoning=f"Error in PoLL ensemble evaluation: {str(e)}",
                key_factors=["System Error"],
                negative_factors=[f"Ensemble processing error: {str(e)}"],
                model_name="PoLL Ensemble (Error)",
                processing_time=processing_time,
                individual_responses=[],
                ensemble_method=self.ensemble_method
            )
    
    def _create_evaluation_prompt(self, loan_data: LoanData) -> str:
        """Create evaluation prompt optimized for financial models."""
        # Simplified prompt for financial models
        financial_prompt = f"""
Analyze this loan application for approval/denial:

{loan_data.to_prompt_context()}

Provide your assessment focusing on:
1. Credit risk level (LOW/MEDIUM/HIGH)
2. Recommendation (APPROVE/DENY/CONDITIONAL)
3. Key concerns or strengths
4. Financial stability indicators

Be concise and focus on quantitative factors.
"""
        return financial_prompt
    
    def _parse_individual_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse individual model response."""
        response_text = response_data.get("response", "")
        model_name = response_data.get("model_name", "Unknown")
        weight = response_data.get("weight", 1.0)
        
        parsed = {
            "model_name": model_name,
            "weight": weight,
            "decision": LoanDecision.DENY,  # Default to deny
            "confidence": ConfidenceLevel.LOW,
            "risk_assessment": RiskLevel.HIGH,
            "reasoning": response_text,
            "key_factors": [],
            "positive_factors": [],
            "negative_factors": [],
            "approval_probability": None,
            "raw_response": response_text
        }
        
        # Simple parsing for financial model responses
        response_upper = response_text.upper()
        
        # Extract decision
        if any(word in response_upper for word in ["APPROVE", "ACCEPT", "POSITIVE", "BULLISH"]):
            parsed["decision"] = LoanDecision.APPROVE
            parsed["confidence"] = ConfidenceLevel.MEDIUM
            parsed["risk_assessment"] = RiskLevel.MEDIUM
        elif any(word in response_upper for word in ["DENY", "REJECT", "NEGATIVE", "BEARISH"]):
            parsed["decision"] = LoanDecision.DENY
            parsed["confidence"] = ConfidenceLevel.MEDIUM
            parsed["risk_assessment"] = RiskLevel.HIGH
        else:
            parsed["decision"] = LoanDecision.CONDITIONAL
            parsed["confidence"] = ConfidenceLevel.LOW
            parsed["risk_assessment"] = RiskLevel.MEDIUM
        
        # Extract key factors from response
        if "high" in response_upper and ("fico" in response_upper or "credit" in response_upper):
            parsed["positive_factors"].append("High credit score")
        if "low" in response_upper and ("dti" in response_upper or "debt" in response_upper):
            parsed["positive_factors"].append("Low debt-to-income ratio")
        if "stable" in response_upper and ("income" in response_upper or "employment" in response_upper):
            parsed["positive_factors"].append("Stable income/employment")
        
        if "high" in response_upper and ("risk" in response_upper or "dti" in response_upper):
            parsed["negative_factors"].append("High risk indicators")
        if "low" in response_upper and ("fico" in response_upper or "credit" in response_upper):
            parsed["negative_factors"].append("Low credit score")
        
        return parsed
    
    def _combine_responses(self, parsed_responses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Combine individual model responses into final decision."""
        if not parsed_responses:
            return {
                "decision": LoanDecision.DENY,
                "confidence": ConfidenceLevel.VERY_LOW,
                "risk_assessment": RiskLevel.VERY_HIGH,
                "reasoning": "No valid responses from ensemble models",
                "key_factors": ["No Model Responses"],
                "positive_factors": [],
                "negative_factors": ["Ensemble failure"]
            }
        
        # Weighted voting for decision
        decision_scores = {"APPROVE": 0, "DENY": 0, "CONDITIONAL": 0}
        total_weight = 0
        
        for response in parsed_responses:
            weight = response.get("weight", 1.0)
            decision = response["decision"].value
            decision_scores[decision] += weight
            total_weight += weight
        
        # Normalize scores
        if total_weight > 0:
            for decision in decision_scores:
                decision_scores[decision] /= total_weight
        
        # Get final decision
        final_decision_str = max(decision_scores, key=decision_scores.get)
        final_decision = LoanDecision(final_decision_str)
        
        # Calculate confidence based on consensus
        max_score = max(decision_scores.values())
        if max_score >= 0.8:
            confidence = ConfidenceLevel.HIGH
        elif max_score >= 0.6:
            confidence = ConfidenceLevel.MEDIUM
        else:
            confidence = ConfidenceLevel.LOW
        
        # Calculate risk assessment
        risk_levels = [resp["risk_assessment"] for resp in parsed_responses]
        risk_values = {"VERY_LOW": 1, "LOW": 2, "MEDIUM": 3, "HIGH": 4, "VERY_HIGH": 5}
        avg_risk = statistics.mean([risk_values[risk.value] for risk in risk_levels])
        
        if avg_risk <= 1.5:
            risk_assessment = RiskLevel.VERY_LOW
        elif avg_risk <= 2.5:
            risk_assessment = RiskLevel.LOW
        elif avg_risk <= 3.5:
            risk_assessment = RiskLevel.MEDIUM
        elif avg_risk <= 4.5:
            risk_assessment = RiskLevel.HIGH
        else:
            risk_assessment = RiskLevel.VERY_HIGH
        
        # Combine factors
        all_positive = []
        all_negative = []
        all_key_factors = []
        
        for response in parsed_responses:
            all_positive.extend(response.get("positive_factors", []))
            all_negative.extend(response.get("negative_factors", []))
            all_key_factors.extend(response.get("key_factors", []))
        
        # Remove duplicates while preserving order
        positive_factors = list(dict.fromkeys(all_positive))[:5]
        negative_factors = list(dict.fromkeys(all_negative))[:5]
        key_factors = list(dict.fromkeys(all_key_factors))[:5]
        
        # Create reasoning
        reasoning = f"Ensemble decision based on {len(parsed_responses)} financial models:\n"
        reasoning += f"Decision distribution: {decision_scores}\n"
        reasoning += f"Consensus strength: {max_score:.2f}\n\n"
        
        for i, response in enumerate(parsed_responses):
            model_name = response.get("model_name", f"Model {i+1}")
            decision = response["decision"].value
            weight = response.get("weight", 1.0)
            reasoning += f"{model_name} (weight: {weight:.2f}): {decision}\n"
        
        return {
            "decision": final_decision,
            "confidence": confidence,
            "risk_assessment": risk_assessment,
            "reasoning": reasoning,
            "key_factors": key_factors,
            "positive_factors": positive_factors,
            "negative_factors": negative_factors,
            "decision_scores": decision_scores
        }
    
    def _calculate_consensus_metrics(self, parsed_responses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate consensus metrics for the ensemble."""
        if not parsed_responses:
            return {"consensus_score": 0.0, "disagreement_factors": ["No responses"]}
        
        # Calculate decision consensus
        decisions = [resp["decision"].value for resp in parsed_responses]
        decision_counts = {}
        for decision in decisions:
            decision_counts[decision] = decision_counts.get(decision, 0) + 1
        
        max_count = max(decision_counts.values())
        consensus_score = max_count / len(decisions)
        
        # Identify disagreement factors
        disagreement_factors = []
        if consensus_score < 0.6:
            disagreement_factors.append("Low decision consensus")
        
        if len(set(decisions)) == 3:  # All three decisions present
            disagreement_factors.append("High decision diversity")
        
        return {
            "consensus_score": consensus_score,
            "disagreement_factors": disagreement_factors
        }
    
    def _get_model_weights(self) -> Dict[str, float]:
        """Get model weights from ensemble."""
        weights = {}
        for i, (provider, weight) in enumerate(zip(self.ensemble.providers, self.ensemble.weights)):
            weights[provider.model_name] = weight
        return weights
    
    def get_approach_info(self) -> Dict[str, Any]:
        """Get information about this approach."""
        info = super().get_approach_info()
        info.update({
            "ensemble_method": self.ensemble_method,
            "num_models": len(self.ensemble.providers),
            "model_weights": self._get_model_weights(),
            "models": [provider.model_name for provider in self.ensemble.providers]
        })
        return info
    
    def cleanup(self):
        """Clean up ensemble resources."""
        if self.ensemble:
            self.ensemble.cleanup()

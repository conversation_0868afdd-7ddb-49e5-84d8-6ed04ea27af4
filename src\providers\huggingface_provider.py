"""
Hugging Face provider implementation for PoLL approach.
"""

import os
from typing import Any, Dict, Optional
import torch
from transformers import <PERSON>Tokenizer, AutoModelForSequenceClassification, pipeline
from transformers import AutoModelForCausalLM, AutoConfig

from .base import <PERSON><PERSON>FaceProvider, LLMConfig


class HuggingFaceFinancialProvider(HuggingFaceProvider):
    """Hugging Face provider for financial models."""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.api_key = config.api_key or os.getenv("HUGGINGFACE_API_KEY")
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.model = None
        self.tokenizer = None
        self.pipeline = None
        
    def _initialize_client(self) -> Any:
        """Initialize the Hugging Face model and tokenizer."""
        return self._load_model_and_tokenizer()
    
    def _load_model_and_tokenizer(self):
        """Load the Hugging Face model and tokenizer."""
        try:
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                token=self.api_key,
                trust_remote_code=True
            )
            
            # Add padding token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Determine model type and load accordingly
            if "finbert" in self.model_name.lower():
                # FinBERT for financial sentiment/classification
                self.model = AutoModelForSequenceClassification.from_pretrained(
                    self.model_name,
                    token=self.api_key,
                    trust_remote_code=True
                )
                self.pipeline = pipeline(
                    "text-classification",
                    model=self.model,
                    tokenizer=self.tokenizer,
                    device=0 if self.device == "cuda" else -1
                )
            
            elif "sec-bert" in self.model_name.lower():
                # SEC-BERT for financial document analysis
                self.model = AutoModelForSequenceClassification.from_pretrained(
                    self.model_name,
                    token=self.api_key,
                    trust_remote_code=True
                )
                self.pipeline = pipeline(
                    "text-classification",
                    model=self.model,
                    tokenizer=self.tokenizer,
                    device=0 if self.device == "cuda" else -1
                )
            
            else:
                # General language model
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_name,
                    token=self.api_key,
                    trust_remote_code=True,
                    torch_dtype=torch.float16 if self.device == "cuda" else torch.float32
                )
                self.pipeline = pipeline(
                    "text-generation",
                    model=self.model,
                    tokenizer=self.tokenizer,
                    device=0 if self.device == "cuda" else -1
                )
            
            # Move model to device
            if self.model:
                self.model.to(self.device)
            
            return self.pipeline
            
        except Exception as e:
            raise Exception(f"Failed to load Hugging Face model {self.model_name}: {str(e)}")
    
    def _generate_response(self, prompt: str, **kwargs) -> str:
        """Generate response from Hugging Face model."""
        if self.pipeline is None:
            self.get_client()
        
        try:
            if "finbert" in self.model_name.lower() or "sec-bert" in self.model_name.lower():
                # Classification models - return sentiment/classification
                result = self.pipeline(prompt)
                if isinstance(result, list) and len(result) > 0:
                    # Format classification result
                    top_result = result[0]
                    label = top_result.get('label', 'UNKNOWN')
                    score = top_result.get('score', 0.0)
                    
                    # Map financial sentiment to loan decision context
                    if label.upper() in ['POSITIVE', 'BULLISH']:
                        decision_context = "APPROVE"
                        confidence = "HIGH" if score > 0.8 else "MEDIUM"
                    elif label.upper() in ['NEGATIVE', 'BEARISH']:
                        decision_context = "DENY"
                        confidence = "HIGH" if score > 0.8 else "MEDIUM"
                    else:
                        decision_context = "CONDITIONAL"
                        confidence = "LOW"
                    
                    return f"Financial Analysis: {label} (confidence: {score:.3f})\nLoan Recommendation: {decision_context}\nConfidence Level: {confidence}"
                else:
                    return "Unable to classify financial context"
            
            else:
                # Text generation models
                max_length = kwargs.get("max_length", 200)
                temperature = kwargs.get("temperature", self.config.temperature)
                
                result = self.pipeline(
                    prompt,
                    max_length=max_length,
                    temperature=temperature,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    **kwargs
                )
                
                if isinstance(result, list) and len(result) > 0:
                    generated_text = result[0].get('generated_text', '')
                    # Remove the original prompt from the response
                    if generated_text.startswith(prompt):
                        generated_text = generated_text[len(prompt):].strip()
                    return generated_text
                else:
                    return "Unable to generate response"
                    
        except Exception as e:
            return f"Error generating response: {str(e)}"
    
    def validate_config(self) -> bool:
        """Validate Hugging Face configuration."""
        # Check if model name is valid for financial analysis
        financial_models = [
            "ProsusAI/finbert",
            "nlpaueb/sec-bert-base",
            "microsoft/DialoGPT-medium",
            "yiyanghkust/finbert-tone",
            "ElKulako/cryptobert"
        ]
        
        return any(model in self.model_name for model in financial_models)
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get Hugging Face model information."""
        info = super().get_model_info()
        info.update({
            "device": self.device,
            "model_loaded": self.model is not None,
            "tokenizer_loaded": self.tokenizer is not None,
            "pipeline_ready": self.pipeline is not None,
            "supported_features": ["financial_analysis", "text_classification", "sentiment_analysis"]
        })
        return info
    
    def cleanup(self):
        """Clean up model resources."""
        if self.model:
            del self.model
        if self.tokenizer:
            del self.tokenizer
        if self.pipeline:
            del self.pipeline
        
        # Clear CUDA cache if using GPU
        if torch.cuda.is_available():
            torch.cuda.empty_cache()


class HuggingFaceEnsemble:
    """Ensemble of Hugging Face models for PoLL approach."""
    
    def __init__(self, model_configs: list):
        self.providers = []
        self.weights = []
        
        for config_dict in model_configs:
            config = LLMConfig(**config_dict)
            provider = HuggingFaceFinancialProvider(config)
            weight = config_dict.get("weight", 1.0)
            
            self.providers.append(provider)
            self.weights.append(weight)
        
        # Normalize weights
        total_weight = sum(self.weights)
        self.weights = [w / total_weight for w in self.weights]
    
    def generate_ensemble_response(self, prompt: str, method: str = "weighted_average") -> Dict[str, Any]:
        """Generate ensemble response from multiple models."""
        individual_responses = []
        
        for provider, weight in zip(self.providers, self.weights):
            try:
                response_data = provider.generate(prompt)
                response_data["weight"] = weight
                individual_responses.append(response_data)
            except Exception as e:
                # Log error but continue with other models
                individual_responses.append({
                    "response": None,
                    "error": str(e),
                    "model_name": provider.model_name,
                    "weight": weight,
                    "success": False
                })
        
        # Combine responses based on method
        if method == "weighted_average":
            return self._weighted_average_combination(individual_responses)
        elif method == "majority_vote":
            return self._majority_vote_combination(individual_responses)
        else:
            return self._simple_combination(individual_responses)
    
    def _weighted_average_combination(self, responses: list) -> Dict[str, Any]:
        """Combine responses using weighted average."""
        successful_responses = [r for r in responses if r.get("success", False)]
        
        if not successful_responses:
            return {
                "response": "All models failed to generate responses",
                "success": False,
                "individual_responses": responses
            }
        
        # Simple combination - in practice, you'd want more sophisticated logic
        combined_response = "Ensemble Analysis:\n"
        for resp in successful_responses:
            weight = resp.get("weight", 0)
            model_name = resp.get("model_name", "Unknown")
            response_text = resp.get("response", "")
            combined_response += f"\n{model_name} (weight: {weight:.2f}):\n{response_text}\n"
        
        return {
            "response": combined_response,
            "success": True,
            "individual_responses": responses,
            "ensemble_method": "weighted_average"
        }
    
    def _majority_vote_combination(self, responses: list) -> Dict[str, Any]:
        """Combine responses using majority vote."""
        # Simplified majority vote - extract decisions and vote
        decisions = []
        for resp in responses:
            if resp.get("success", False):
                response_text = resp.get("response", "").upper()
                if "APPROVE" in response_text:
                    decisions.append("APPROVE")
                elif "DENY" in response_text:
                    decisions.append("DENY")
                else:
                    decisions.append("CONDITIONAL")
        
        if not decisions:
            return {
                "response": "No valid decisions from ensemble",
                "success": False,
                "individual_responses": responses
            }
        
        # Count votes
        vote_counts = {}
        for decision in decisions:
            vote_counts[decision] = vote_counts.get(decision, 0) + 1
        
        # Get majority decision
        majority_decision = max(vote_counts, key=vote_counts.get)
        
        combined_response = f"Ensemble Decision: {majority_decision}\n"
        combined_response += f"Vote Distribution: {vote_counts}\n"
        combined_response += "Individual Model Responses:\n"
        
        for resp in responses:
            if resp.get("success", False):
                model_name = resp.get("model_name", "Unknown")
                response_text = resp.get("response", "")
                combined_response += f"\n{model_name}:\n{response_text}\n"
        
        return {
            "response": combined_response,
            "success": True,
            "individual_responses": responses,
            "ensemble_method": "majority_vote",
            "vote_distribution": vote_counts
        }
    
    def _simple_combination(self, responses: list) -> Dict[str, Any]:
        """Simple combination of responses."""
        successful_responses = [r for r in responses if r.get("success", False)]
        
        combined_response = "Combined Model Responses:\n"
        for resp in successful_responses:
            model_name = resp.get("model_name", "Unknown")
            response_text = resp.get("response", "")
            combined_response += f"\n{model_name}:\n{response_text}\n"
        
        return {
            "response": combined_response,
            "success": len(successful_responses) > 0,
            "individual_responses": responses,
            "ensemble_method": "simple_combination"
        }
    
    def cleanup(self):
        """Clean up all model resources."""
        for provider in self.providers:
            provider.cleanup()

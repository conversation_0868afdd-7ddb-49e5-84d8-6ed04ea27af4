"""
Anthropic Claude provider implementation.
"""

import os
from typing import Any, Dict
from langchain_anthropic import <PERSON>t<PERSON>nt<PERSON>
from langchain_core.messages import HumanMessage

from .base import <PERSON><PERSON>rovider, LLMConfig


class AnthropicProvider(LLMProvider):
    """Anthropic Claude provider."""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.api_key = config.api_key or os.getenv("ANTHROPIC_API_KEY")
        if not self.api_key:
            raise ValueError("Anthropic API key not found. Set ANTHROPIC_API_KEY environment variable.")
    
    def _initialize_client(self) -> ChatAnthropic:
        """Initialize the Anthropic client."""
        return ChatAnthropic(
            model=self.model_name,
            temperature=self.config.temperature,
            max_tokens=self.config.max_tokens,
            anthropic_api_key=self.api_key,
            **self.config.additional_params
        )
    
    def _generate_response(self, prompt: str, **kwargs) -> str:
        """Generate response from <PERSON>."""
        client = self.get_client()
        
        # Override config parameters with kwargs if provided
        temp_params = {}
        if "temperature" in kwargs:
            temp_params["temperature"] = kwargs["temperature"]
        if "max_tokens" in kwargs:
            temp_params["max_tokens"] = kwargs["max_tokens"]
        
        # Create a temporary client with updated parameters if needed
        if temp_params:
            temp_client = ChatAnthropic(
                model=self.model_name,
                temperature=temp_params.get("temperature", self.config.temperature),
                max_tokens=temp_params.get("max_tokens", self.config.max_tokens),
                anthropic_api_key=self.api_key,
                **self.config.additional_params
            )
            response = temp_client.invoke([HumanMessage(content=prompt)])
        else:
            response = client.invoke([HumanMessage(content=prompt)])
        
        return response.content
    
    def validate_config(self) -> bool:
        """Validate Anthropic configuration."""
        if not self.api_key:
            return False
        
        # Check if model name is valid for Anthropic
        valid_models = [
            "claude-3-5-sonnet-20241022",
            "claude-3-5-haiku-20241022",
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229",
            "claude-3-haiku-20240307"
        ]
        
        return self.model_name in valid_models
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get Anthropic model information."""
        info = super().get_model_info()
        info.update({
            "api_key_configured": bool(self.api_key),
            "supported_features": ["text_generation", "conversation", "function_calling"]
        })
        return info

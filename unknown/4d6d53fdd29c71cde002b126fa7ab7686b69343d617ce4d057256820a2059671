#!/usr/bin/env python3
"""
Test script for the loan approval prediction system using real CSV data and LLM API calls.
"""

from src.evaluation.benchmarking import LoanApprovalEvaluator
from src.utils.data_loader import LoanDataLoader
from src.providers.factory import create_frontier_model
from src.approaches.single_frontier import SingleFrontierApproach
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))


def test_csv_data_loading(csv_file: str, max_records: int = 3):
    """Test loading data from CSV file."""
    print("🧪 Testing CSV Data Loading")
    print("-" * 40)
    print(f"� Loading data from: {csv_file}")

    try:
        data_loader = LoanDataLoader()
        loan_data = data_loader.load_csv(csv_file, max_records=max_records)

        if not loan_data:
            print("❌ No loan data loaded from CSV")
            return None

        print(f"✅ Loaded {len(loan_data)} loans from CSV")

        # Show sample of loaded data
        for i, loan in enumerate(loan_data[:2]):  # Show first 2 loans
            print(f"\n📊 Loan {i+1} from CSV:")
            print(f"  Amount: ${loan.loan_amnt:,.2f}")
            print(f"  Grade: {loan.grade.value}")
            print(f"  Annual Income: ${loan.annual_inc:,.2f}")
            print(f"  DTI: {loan.dti:.2f}%")
            print(f"  Purpose: {loan.purpose.value.replace('_', ' ').title()}")
            if loan.loan_status:
                print(f"  Status: {loan.loan_status.value}")

        return loan_data

    except Exception as e:
        print(f"❌ Error loading CSV data: {e}")
        return None


def test_gemini_llm_calls(loan_data, max_tests: int = 2):
    """Test actual Gemini LLM API calls with CSV data."""
    print("\n🧪 Testing Gemini LLM API Calls")
    print("-" * 40)
    print("🤖 Initializing Gemini LLM from config...")

    try:
        # Initialize Gemini LLM using config
        frontier_provider = create_frontier_model("config.yaml")
        single_approach = SingleFrontierApproach(frontier_provider)

        print(f"✅ Initialized {frontier_provider.model_name}")
        print(f"   Provider: {frontier_provider.provider_name}")

        # Test predictions on actual CSV data
        test_loans = loan_data[:max_tests]
        print(f"\n📊 Testing {len(test_loans)} loans from CSV...")

        for i, loan in enumerate(test_loans, 1):
            print(f"\n--- Test {i} ---")
            print(f"Loan Amount: ${loan.loan_amnt:,.2f}")
            print(f"Grade: {loan.grade.value}")
            print(f"Annual Income: ${loan.annual_inc:,.2f}")
            print(f"DTI: {loan.dti:.2f}%")

            try:
                print("🔄 Making API call to Gemini...")
                prediction = single_approach.predict_single(loan)

                print(f"✅ Prediction received:")
                print(f"   Decision: {prediction.decision.value}")
                print(f"   Confidence: {prediction.confidence.value}")
                print(f"   Risk Assessment: {prediction.risk_assessment.value}")
                print(f"   Processing Time: {prediction.processing_time:.3f}s")
                print(f"   Reasoning: {prediction.reasoning[:200]}...")

                if loan.loan_status:
                    actual_good = loan.is_good_loan()
                    predicted_approve = prediction.decision.value == "APPROVE"
                    correct = actual_good == predicted_approve
                    print(f"   Actual Status: {loan.loan_status.value}")
                    print(f"   Prediction Correct: {'✅' if correct else '❌'}")

            except Exception as e:
                print(f"❌ API call failed: {e}")

        return True

    except Exception as e:
        print(f"❌ Failed to initialize Gemini LLM: {e}")
        print("💡 Make sure your .env file has GOOGLE_API_KEY set")
        return False


def test_evaluation_system_with_csv(loan_data):
    """Test the evaluation system with real CSV data."""
    print("\n🧪 Testing Evaluation System with CSV Data")
    print("-" * 40)

    try:
        evaluator = LoanApprovalEvaluator()

        # Filter loans with status for evaluation
        loans_with_status = [loan for loan in loan_data if loan.loan_status is not None]

        if not loans_with_status:
            print("⚠️  No loans with status found for evaluation testing")
            return False

        print(f"📊 Found {len(loans_with_status)} loans with status for evaluation")

        # Show evaluation data summary
        good_loans = sum(1 for loan in loans_with_status if loan.is_good_loan())
        bad_loans = len(loans_with_status) - good_loans

        print(f"   Good loans: {good_loans}")
        print(f"   Bad loans: {bad_loans}")
        print(f"   Good loan ratio: {good_loans/len(loans_with_status):.3f}")

        print("✅ Evaluation system ready for CSV data")
        return True

    except Exception as e:
        print(f"❌ Error testing evaluation system: {e}")
        return False


# All testing now uses real CSV data and LLM API calls


def main():
    """Run all tests using real CSV data and LLM API calls."""
    print("🏦 Loan Approval Prediction System - Test Suite")
    print("=" * 60)
    print("� Testing with REAL CSV data and LLM API calls")
    print()

    # Default CSV file to test with
    csv_file = "data/Lending Club loan data/loan_data_exported.csv"

    # Check if CSV file exists
    if not Path(csv_file).exists():
        print(f"❌ CSV file not found: {csv_file}")
        print("Please ensure you have loan data CSV file available")
        return

    # Test 1: Load CSV data
    loan_data = test_csv_data_loading(csv_file, max_records=5)
    if not loan_data:
        print("❌ Cannot proceed without CSV data")
        return

    # Test 2: Test evaluation system with CSV data
    test_evaluation_system_with_csv(loan_data)

    # Test 3: Test actual LLM API calls with CSV data
    print("\n" + "="*60)
    print("🚀 TESTING REAL LLM API CALLS")
    print("="*60)

    success = test_gemini_llm_calls(loan_data, max_tests=2)

    if success:
        print(f"\n✅ All tests completed successfully!")
        print(f"🎯 The system is working with:")
        print(f"   - Real CSV data from: {csv_file}")
        print(f"   - Actual Gemini LLM API calls")
        print(f"   - Complete evaluation pipeline")
    else:
        print(f"\n⚠️  Some tests failed. Check your .env file and API keys.")

    print(f"\n🚀 To run full evaluation:")
    print(f"   python main.py --csv-file \"{csv_file}\" --approach single --max-records 10")


if __name__ == "__main__":
    main()

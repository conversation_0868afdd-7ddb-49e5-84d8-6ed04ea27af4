"""
Base classes for LLM providers.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
import time
from datetime import datetime


class LLMConfig(BaseModel):
    """Configuration for LLM providers."""
    provider: str
    model_name: str
    temperature: float = 0.1
    max_tokens: int = 2000
    api_key: Optional[str] = None
    additional_params: Dict[str, Any] = {}


class LLMProvider(ABC):
    """Abstract base class for LLM providers."""
    
    def __init__(self, config: LLMConfig):
        self.config = config
        self.provider_name = config.provider
        self.model_name = config.model_name
        self._client = None
    
    @abstractmethod
    def _initialize_client(self) -> Any:
        """Initialize the LLM client."""
        pass
    
    @abstractmethod
    def _generate_response(self, prompt: str, **kwargs) -> str:
        """Generate response from the LLM."""
        pass
    
    def get_client(self) -> Any:
        """Get or initialize the LLM client."""
        if self._client is None:
            self._client = self._initialize_client()
        return self._client
    
    def generate(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """
        Generate response with timing and metadata.
        
        Args:
            prompt: The input prompt
            **kwargs: Additional parameters for the specific provider
            
        Returns:
            Dictionary containing response and metadata
        """
        start_time = time.time()
        
        try:
            response = self._generate_response(prompt, **kwargs)
            processing_time = time.time() - start_time
            
            return {
                "response": response,
                "model_name": self.model_name,
                "provider": self.provider_name,
                "processing_time": processing_time,
                "timestamp": datetime.now(),
                "success": True,
                "error": None
            }
        
        except Exception as e:
            processing_time = time.time() - start_time
            
            return {
                "response": None,
                "model_name": self.model_name,
                "provider": self.provider_name,
                "processing_time": processing_time,
                "timestamp": datetime.now(),
                "success": False,
                "error": str(e)
            }
    
    def validate_config(self) -> bool:
        """Validate the provider configuration."""
        return True
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the model."""
        return {
            "provider": self.provider_name,
            "model_name": self.model_name,
            "temperature": self.config.temperature,
            "max_tokens": self.config.max_tokens
        }


class HuggingFaceProvider(LLMProvider):
    """Base class for Hugging Face model providers."""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.weight = config.additional_params.get("weight", 1.0)
    
    @abstractmethod
    def _load_model_and_tokenizer(self):
        """Load the Hugging Face model and tokenizer."""
        pass
    
    def get_weight(self) -> float:
        """Get the weight for ensemble methods."""
        return self.weight

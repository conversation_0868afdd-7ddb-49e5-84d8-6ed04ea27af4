"""
Factory pattern for LLM provider creation.
"""

from typing import Dict, Any, Optional, Union
import yaml
from pathlib import Path

from .base import <PERSON><PERSON><PERSON><PERSON>, LLMConfig
from .anthropic_provider import AnthropicProvider
from .openai_provider import OpenAIProvider
from .google_provider import GoogleProvider
from .groq_provider import GroqProvider
from .huggingface_provider import HuggingFaceFinancialProvider, HuggingFaceEnsemble


class LLMProviderFactory:
    """Factory for creating LLM providers based on configuration."""
    
    _provider_classes = {
        "anthropic": AnthropicProvider,
        "openai": OpenAIProvider,
        "google": GoogleProvider,
        "groq": GroqProvider,
        "huggingface": HuggingFaceFinancialProvider
    }
    
    @classmethod
    def create_provider(cls, provider_name: str, config: Union[Dict[str, Any], LLMConfig]) -> LLMProvider:
        """
        Create an LLM provider instance.
        
        Args:
            provider_name: Name of the provider (anthropic, openai, google, groq, huggingface)
            config: Configuration dictionary or LLMConfig instance
            
        Returns:
            LLMProvider instance
            
        Raises:
            ValueError: If provider name is not supported
        """
        if provider_name not in cls._provider_classes:
            available_providers = ", ".join(cls._provider_classes.keys())
            raise ValueError(f"Unsupported provider: {provider_name}. Available providers: {available_providers}")
        
        # Convert dict to LLMConfig if needed
        if isinstance(config, dict):
            config = LLMConfig(**config)
        
        provider_class = cls._provider_classes[provider_name]
        return provider_class(config)
    
    @classmethod
    def create_from_config_file(cls, config_path: str, provider_key: str) -> LLMProvider:
        """
        Create provider from YAML configuration file.
        
        Args:
            config_path: Path to YAML configuration file
            provider_key: Key in config file (e.g., 'models.frontier', 'models.court.judge_model')
            
        Returns:
            LLMProvider instance
        """
        config_file = Path(config_path)
        if not config_file.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        with open(config_file, 'r') as f:
            config_data = yaml.safe_load(f)
        
        # Navigate to the specified key
        keys = provider_key.split('.')
        current_config = config_data
        
        for key in keys:
            if key not in current_config:
                raise KeyError(f"Configuration key '{provider_key}' not found in {config_path}")
            current_config = current_config[key]
        
        provider_name = current_config.get('provider')
        if not provider_name:
            raise ValueError(f"Provider name not specified in configuration key '{provider_key}'")
        
        return cls.create_provider(provider_name, current_config)
    
    @classmethod
    def create_ensemble(cls, model_configs: list) -> HuggingFaceEnsemble:
        """
        Create a Hugging Face ensemble for PoLL approach.
        
        Args:
            model_configs: List of model configuration dictionaries
            
        Returns:
            HuggingFaceEnsemble instance
        """
        return HuggingFaceEnsemble(model_configs)
    
    @classmethod
    def get_available_providers(cls) -> list:
        """Get list of available provider names."""
        return list(cls._provider_classes.keys())
    
    @classmethod
    def validate_provider_config(cls, provider_name: str, config: Dict[str, Any]) -> bool:
        """
        Validate provider configuration.
        
        Args:
            provider_name: Name of the provider
            config: Configuration dictionary
            
        Returns:
            True if configuration is valid
        """
        try:
            provider = cls.create_provider(provider_name, config)
            return provider.validate_config()
        except Exception:
            return False


class ConfigManager:
    """Manager for loading and validating configuration."""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file."""
        config_file = Path(self.config_path)
        if not config_file.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        
        with open(config_file, 'r') as f:
            return yaml.safe_load(f)
    
    def get_frontier_model_config(self) -> Dict[str, Any]:
        """Get frontier model configuration."""
        return self.config.get('models', {}).get('frontier', {})
    
    def get_poll_models_config(self) -> list:
        """Get PoLL models configuration."""
        return self.config.get('models', {}).get('poll', {}).get('models', [])
    
    def get_court_models_config(self) -> Dict[str, Dict[str, Any]]:
        """Get court system models configuration."""
        court_config = self.config.get('models', {}).get('court', {})
        return {
            'pros_model': court_config.get('pros_model', {}),
            'cons_model': court_config.get('cons_model', {}),
            'judge_model': court_config.get('judge_model', {})
        }
    
    def get_data_config(self) -> Dict[str, Any]:
        """Get data processing configuration."""
        return self.config.get('data', {})
    
    def get_evaluation_config(self) -> Dict[str, Any]:
        """Get evaluation configuration."""
        return self.config.get('evaluation', {})
    
    def get_langfuse_config(self) -> Dict[str, Any]:
        """Get Langfuse configuration."""
        return self.config.get('langfuse', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration."""
        return self.config.get('logging', {})
    
    def get_output_config(self) -> Dict[str, Any]:
        """Get output configuration."""
        return self.config.get('output', {})
    
    def validate_all_configs(self) -> Dict[str, bool]:
        """Validate all provider configurations."""
        results = {}
        
        # Validate frontier model
        frontier_config = self.get_frontier_model_config()
        if frontier_config:
            provider_name = frontier_config.get('provider')
            results['frontier'] = LLMProviderFactory.validate_provider_config(provider_name, frontier_config)
        
        # Validate PoLL models
        poll_configs = self.get_poll_models_config()
        for i, poll_config in enumerate(poll_configs):
            provider_name = poll_config.get('provider')
            results[f'poll_model_{i}'] = LLMProviderFactory.validate_provider_config(provider_name, poll_config)
        
        # Validate court models
        court_configs = self.get_court_models_config()
        for model_type, court_config in court_configs.items():
            if court_config:
                provider_name = court_config.get('provider')
                results[f'court_{model_type}'] = LLMProviderFactory.validate_provider_config(provider_name, court_config)
        
        return results
    
    def reload_config(self):
        """Reload configuration from file."""
        self.config = self._load_config()


# Convenience functions for common use cases
def create_frontier_model(config_path: str = "config.yaml") -> LLMProvider:
    """Create frontier model from configuration."""
    factory = LLMProviderFactory()
    return factory.create_from_config_file(config_path, "models.frontier")


def create_poll_ensemble(config_path: str = "config.yaml") -> HuggingFaceEnsemble:
    """Create PoLL ensemble from configuration."""
    config_manager = ConfigManager(config_path)
    poll_configs = config_manager.get_poll_models_config()
    return LLMProviderFactory.create_ensemble(poll_configs)


def create_court_models(config_path: str = "config.yaml") -> Dict[str, LLMProvider]:
    """Create court system models from configuration."""
    factory = LLMProviderFactory()
    
    return {
        'pros_model': factory.create_from_config_file(config_path, "models.court.pros_model"),
        'cons_model': factory.create_from_config_file(config_path, "models.court.cons_model"),
        'judge_model': factory.create_from_config_file(config_path, "models.court.judge_model")
    }

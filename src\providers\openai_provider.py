"""
OpenAI provider implementation.
"""

import os
from typing import Any, Dict
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage

from .base import <PERSON><PERSON><PERSON>ider, LLMConfig


class OpenAIProvider(LLMProvider):
    """OpenAI GPT provider."""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.api_key = config.api_key or os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OpenAI API key not found. Set OPENAI_API_KEY environment variable.")
    
    def _initialize_client(self) -> ChatOpenAI:
        """Initialize the OpenAI client."""
        return ChatOpenAI(
            model=self.model_name,
            temperature=self.config.temperature,
            max_tokens=self.config.max_tokens,
            openai_api_key=self.api_key,
            **self.config.additional_params
        )
    
    def _generate_response(self, prompt: str, **kwargs) -> str:
        """Generate response from GPT."""
        client = self.get_client()
        
        # Override config parameters with kwargs if provided
        temp_params = {}
        if "temperature" in kwargs:
            temp_params["temperature"] = kwargs["temperature"]
        if "max_tokens" in kwargs:
            temp_params["max_tokens"] = kwargs["max_tokens"]
        
        # Create a temporary client with updated parameters if needed
        if temp_params:
            temp_client = ChatOpenAI(
                model=self.model_name,
                temperature=temp_params.get("temperature", self.config.temperature),
                max_tokens=temp_params.get("max_tokens", self.config.max_tokens),
                openai_api_key=self.api_key,
                **self.config.additional_params
            )
            response = temp_client.invoke([HumanMessage(content=prompt)])
        else:
            response = client.invoke([HumanMessage(content=prompt)])
        
        return response.content
    
    def validate_config(self) -> bool:
        """Validate OpenAI configuration."""
        if not self.api_key:
            return False
        
        # Check if model name is valid for OpenAI
        valid_models = [
            "gpt-4-turbo-preview",
            "gpt-4-turbo",
            "gpt-4",
            "gpt-4-32k",
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k"
        ]
        
        return any(self.model_name.startswith(model) for model in valid_models)
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get OpenAI model information."""
        info = super().get_model_info()
        info.update({
            "api_key_configured": bool(self.api_key),
            "supported_features": ["text_generation", "conversation", "function_calling", "vision"]
        })
        return info
